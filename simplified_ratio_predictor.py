#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Enhanced Ratio Predictor - Focus on improving accuracy
Based on simplified strategies, avoiding overfitting
"""

import pandas as pd
import numpy as np
from typing import Tuple, Dict, List
import random

class SimplifiedRatioPredictor:
    """简化的比例预测器 - 专注准确率而非复杂度"""
    
    def __init__(self):
        self.lookback_periods = [10, 20, 30]  # 多时间窗口
        self.confidence_threshold = 0.7  # 降低过度自信
        
    def predict_red_odd_even_ratio(self, data: pd.DataFrame, current_period: int) -> Tuple[str, float]:
        """预测红球奇偶比 - 简化但有效的策略"""
        if len(data) < 20:
            return "3:2", 0.5
        
        # 策略1: 多时间窗口频率分析
        ratio_scores = {}
        for window in self.lookback_periods:
            recent_data = data.tail(window)
            window_ratios = self._calculate_historical_ratios(recent_data, "odd_even")
            
            # 加权计算 (近期权重更高)
            weight = 1.0 / window  # 近期窗口权重更高
            for ratio, count in window_ratios.items():
                if ratio not in ratio_scores:
                    ratio_scores[ratio] = 0
                ratio_scores[ratio] += count * weight
        
        # 策略2: 反连续模式 (避免过度连续相同预测)
        last_3_periods = data.tail(3)
        last_ratios = []
        for _, row in last_3_periods.iterrows():
            red_balls = [row[f'红球{i}'] for i in range(1, 6)]
            odd_count = sum(1 for x in red_balls if x % 2 == 1)
            last_ratios.append(f"{odd_count}:{5-odd_count}")
        
        # 如果最近3期都是同一比例，降低该比例的权重
        if len(set(last_ratios)) == 1 and len(last_ratios) >= 3:
            repeated_ratio = last_ratios[0]
            if repeated_ratio in ratio_scores:
                ratio_scores[repeated_ratio] *= 0.5  # 降低权重
        
        # 选择最佳预测
        if ratio_scores:
            best_ratio = max(ratio_scores, key=ratio_scores.get)
            total_score = sum(ratio_scores.values())
            confidence = min(ratio_scores[best_ratio] / total_score, self.confidence_threshold)
        else:
            best_ratio = "3:2"  # 默认值
            confidence = 0.4
        
        return best_ratio, confidence
    
    def predict_red_size_ratio(self, data: pd.DataFrame, current_period: int) -> Tuple[str, float]:
        """预测红球大小比"""
        if len(data) < 20:
            return "2:3", 0.5
        
        # 使用类似策略
        ratio_scores = {}
        for window in self.lookback_periods:
            recent_data = data.tail(window)
            window_ratios = self._calculate_historical_ratios(recent_data, "size")
            
            weight = 1.0 / window
            for ratio, count in window_ratios.items():
                if ratio not in ratio_scores:
                    ratio_scores[ratio] = 0
                ratio_scores[ratio] += count * weight
        
        # 反连续策略
        last_3_periods = data.tail(3)
        last_ratios = []
        for _, row in last_3_periods.iterrows():
            red_balls = [row[f'红球{i}'] for i in range(1, 6)]
            big_count = sum(1 for x in red_balls if x > 18)
            last_ratios.append(f"{big_count}:{5-big_count}")
        
        if len(set(last_ratios)) == 1 and len(last_ratios) >= 3:
            repeated_ratio = last_ratios[0]
            if repeated_ratio in ratio_scores:
                ratio_scores[repeated_ratio] *= 0.5
        
        if ratio_scores:
            best_ratio = max(ratio_scores, key=ratio_scores.get)
            total_score = sum(ratio_scores.values())
            confidence = min(ratio_scores[best_ratio] / total_score, self.confidence_threshold)
        else:
            best_ratio = "2:3"
            confidence = 0.4
        
        return best_ratio, confidence
    
    def predict_blue_size_ratio(self, data: pd.DataFrame, current_period: int) -> Tuple[str, float]:
        """预测蓝球大小比"""
        if len(data) < 15:
            return "1:1", 0.5
        
        # 蓝球预测相对简单
        recent_data = data.tail(20)
        ratio_counts = {}
        
        for _, row in recent_data.iterrows():
            blue_balls = [row[f'蓝球{i}'] for i in range(1, 3)]
            big_count = sum(1 for x in blue_balls if x > 6)
            ratio = f"{big_count}:{2-big_count}"
            ratio_counts[ratio] = ratio_counts.get(ratio, 0) + 1
        
        if ratio_counts:
            best_ratio = max(ratio_counts, key=ratio_counts.get)
            confidence = min(ratio_counts[best_ratio] / len(recent_data), self.confidence_threshold)
        else:
            best_ratio = "1:1"
            confidence = 0.4
        
        return best_ratio, confidence
    
    def _calculate_historical_ratios(self, data: pd.DataFrame, ratio_type: str) -> Dict[str, int]:
        """计算历史比例分布"""
        ratios = {}
        
        for _, row in data.iterrows():
            if ratio_type == "odd_even":
                red_balls = [row[f'红球{i}'] for i in range(1, 6)]
                count = sum(1 for x in red_balls if x % 2 == 1)
                ratio = f"{count}:{5-count}"
            elif ratio_type == "size":
                red_balls = [row[f'红球{i}'] for i in range(1, 6)]
                count = sum(1 for x in red_balls if x > 18)
                ratio = f"{count}:{5-count}"
            else:
                continue
            
            ratios[ratio] = ratios.get(ratio, 0) + 1
        
        return ratios
