#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
from pathlib import Path
import pandas as pd
import time
import logging

project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/lstm_backtest.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class LSTMBacktestSystem:
    def __init__(self):
        self.logger = logging.getLogger(f'lstm_backtest.{self.__class__.__name__}')
        
    def load_data(self) -> pd.DataFrame:
        data_path = Path('data/raw/dlt_data.csv')
        if data_path.exists():
            data = pd.read_csv(data_path)
            self.logger.info(f'加载数据: {len(data)} 条记录')
            return data
        else:
            self.logger.error('数据文件不存在')
            return pd.DataFrame()
    
    def run_lstm_backtest(self, data: pd.DataFrame, test_periods: int = 10):
        try:
            from src.framework import BacktestFramework, BacktestConfig, ResultDisplayer
            from src.framework.predictor_adapter import create_predictor_adapter
            
            print('使用统一回测框架进行LSTM回测...')
            print('=' * 60)
            
            lstm_adapter = create_predictor_adapter('lstm', data)
            framework = BacktestFramework(data)
            
            config = BacktestConfig(
                num_periods=test_periods,
                min_train_periods=0,
                display_periods=test_periods,
                enable_detailed_output=True,
                enable_statistics=True,
                reverse_display=False
            )
            
            start_time = time.time()
            results = framework.run_backtest(lstm_adapter, config)
            end_time = time.time()
            
            print(f'LSTM回测完成，耗时 {end_time - start_time:.2f} 秒')
            
            displayer = ResultDisplayer()
            displayer.display_backtest_result(results)
            
            return results
            
        except ImportError as e:
            self.logger.error(f'统一框架导入失败: {e}')
            print(f'统一框架导入失败: {e}')
            return None
        except Exception as e:
            self.logger.error(f'LSTM回测失败: {e}')
            print(f'LSTM回测失败: {e}')
            return None

def main():
    print('LSTM深度学习回测系统')
    print('使用统一回测框架')
    print('=' * 60)
    
    system = LSTMBacktestSystem()
    
    data = system.load_data()
    if data.empty:
        print('数据加载失败，退出程序')
        return
    
    results = system.run_lstm_backtest(data, test_periods=10)
    
    if results:
        print('\nLSTM回测完成！')
        print('详细结果已显示在上方')
    else:
        print('\nLSTM回测失败！')

if __name__ == '__main__':
    main()
