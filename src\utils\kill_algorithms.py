"""
统一杀号算法模块
整合所有杀号策略，提供统一的接口
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Tuple, Set
from collections import Counter, defaultdict

from .utils import parse_numbers


class UnifiedKillAlgorithms:
    """统一杀号算法类 - 增强版多样化杀号"""
    
    def __init__(self):
        """初始化杀号算法"""
        self.red_range = list(range(1, 36))  # 红球范围1-35
        self.blue_range = list(range(1, 13))  # 蓝球范围1-12
        self.historical_stats = {}
        
        # 杀号历史记录
        self.red_kill_history = []
        self.blue_kill_history = []
        self.red_success_history = []
        self.blue_success_history = []
        
        # 多样化策略权重 (动态调整)
        self.strategy_weights = {
            'frequency': 0.25,
            'consecutive': 0.20,
            'sum_analysis': 0.15,
            'parity': 0.15,
            'zone_analysis': 0.10,
            'gap_analysis': 0.10,
            'random_diversify': 0.05
        }
        
        # 策略成功率跟踪
        self.strategy_performance = {
            strategy: {'hits': 0, 'total': 0, 'success_rate': 0.0}
            for strategy in self.strategy_weights.keys()
        }
    
    def generate_red_kill_numbers(self, data: pd.DataFrame, period_num: int, 
                                 kill_count: int = 10) -> List[int]:
        """
        生成红球杀号 - 增强版多样化策略
        
        Args:
            data: 历史数据
            period_num: 当前期号
            kill_count: 杀号数量 (默认10个，优化后减少)
            
        Returns:
            List[int]: 红球杀号列表
        """
        if len(data) < 5:
            return self._get_fallback_red_kills(kill_count)
        
        # 获取最近数据用于分析
        recent_data = data.tail(10)  # 增加分析窗口
        prev_periods = []
        
        for _, row in recent_data.iterrows():
            red_balls, _ = parse_numbers(row)
            prev_periods.append(red_balls)
        
        # 多策略杀号生成
        strategy_kills = {}
        
        # 策略1: 频率分析杀号
        strategy_kills['frequency'] = self._frequency_kill_strategy(data, 'red', 
                                                                   max(2, int(kill_count * self.strategy_weights['frequency'])))
        
        # 策略2: 连号分析杀号
        strategy_kills['consecutive'] = self._consecutive_kill_strategy(prev_periods, 
                                                                      max(2, int(kill_count * self.strategy_weights['consecutive'])))
        
        # 策略3: 和值分析杀号
        strategy_kills['sum_analysis'] = self._sum_analysis_kill_strategy(prev_periods, 
                                                                         max(1, int(kill_count * self.strategy_weights['sum_analysis'])))
        
        # 策略4: 奇偶分析杀号
        strategy_kills['parity'] = self._parity_kill_strategy(prev_periods, 
                                                             max(1, int(kill_count * self.strategy_weights['parity'])))
        
        # 策略5: 区间分析杀号 (新增)
        strategy_kills['zone_analysis'] = self._zone_analysis_kill_strategy(prev_periods, 
                                                                           max(1, int(kill_count * self.strategy_weights['zone_analysis'])))
        
        # 策略6: 间隔分析杀号 (新增)
        strategy_kills['gap_analysis'] = self._gap_analysis_kill_strategy(prev_periods, 
                                                                         max(1, int(kill_count * self.strategy_weights['gap_analysis'])))
        
        # 策略7: 随机多样化 (新增)
        strategy_kills['random_diversify'] = self._random_diversify_strategy(prev_periods, 
                                                                            max(1, int(kill_count * self.strategy_weights['random_diversify'])))
        
        # 智能合并策略结果
        final_kills = self._smart_merge_strategies(strategy_kills, kill_count)
        
        # 记录本次杀号
        self.red_kill_history.append({
            'period': period_num,
            'kills': final_kills,
            'strategies_used': list(strategy_kills.keys())
        })
        
        return sorted(final_kills[:kill_count])
    
    def generate_blue_kill_numbers(self, data: pd.DataFrame, period_num: int,
                                  kill_count: int = 4) -> List[int]:
        """
        增强版蓝球多样化杀号策略
        
        Args:
            data: 历史数据
            period_num: 当前期号
            kill_count: 杀号数量
            
        Returns:
            List[int]: 蓝球杀号列表
        """
        if len(data) < 2:
            return self._get_fallback_blue_kills(kill_count)
        
        # 获取最近数据用于分析
        recent_data = data.tail(8)  # 扩大分析窗口
        prev_periods = []
        
        for _, row in recent_data.iterrows():
            _, blue_balls = parse_numbers(row)
            prev_periods.append(blue_balls)
        
        # 多样化策略组合
        strategy_kills = {}
        
        # 频率分析 (权重调整)
        freq_count = max(1, int(kill_count * self.strategy_weights.get('frequency', 0.25)))
        strategy_kills['frequency'] = self._frequency_blue_kill_strategy(prev_periods, freq_count)
        
        # 连号分析
        consecutive_count = max(1, int(kill_count * self.strategy_weights.get('consecutive', 0.2)))
        strategy_kills['consecutive'] = self._consecutive_blue_kill_strategy(prev_periods, consecutive_count)
        
        # 奇偶分析 (新增)
        parity_count = max(1, int(kill_count * self.strategy_weights.get('parity', 0.2)))
        strategy_kills['parity'] = self._parity_blue_kill_strategy(prev_periods, parity_count)
        
        # 区间分析 (新增)
        zone_count = max(1, int(kill_count * self.strategy_weights.get('zone_analysis', 0.15)))
        strategy_kills['zone'] = self._zone_blue_kill_strategy(prev_periods, zone_count)
        
        # 随机多样化 (新增)
        random_count = max(1, int(kill_count * self.strategy_weights.get('random_diversify', 0.2)))
        strategy_kills['random'] = self._random_blue_diversify_strategy(prev_periods, random_count)
        
        # 智能合并策略结果
        final_kills = self._smart_merge_blue_strategies(strategy_kills, kill_count)
        
        # 记录杀号历史
        self.blue_kill_history.append({
            'period': period_num,
            'kills': final_kills,
            'strategies_used': list(strategy_kills.keys()),
            'analysis_window': len(prev_periods)
        })
        
        return sorted(final_kills[:kill_count])
    
    def _frequency_kill_strategy(self, data: pd.DataFrame, ball_type: str, 
                               count: int) -> List[int]:
        """频率分析杀号策略"""
        numbers = []
        
        # 收集最近50期的号码
        recent_data = data.tail(50)
        for _, row in recent_data.iterrows():
            red_balls, blue_balls = parse_numbers(row)
            if ball_type == 'red':
                numbers.extend(red_balls)
            else:
                numbers.extend(blue_balls)
        
        # 统计频率，选择最少出现的号码
        number_range = self.red_range if ball_type == 'red' else self.blue_range
        freq_counter = Counter(numbers)
        
        # 找出最少出现的号码
        min_freq = min(freq_counter.get(n, 0) for n in number_range)
        candidates = [n for n in number_range if freq_counter.get(n, 0) == min_freq]
        
        return candidates[:count]
    
    def _consecutive_kill_strategy(self, prev_periods: List[List[int]], 
                                 count: int) -> List[int]:
        """连号分析杀号策略"""
        if len(prev_periods) < 2:
            return []
        
        # 分析连号模式
        all_numbers = []
        for period in prev_periods:
            all_numbers.extend(period)
        
        # 找出连续出现的号码模式，避免这些号码
        consecutive_patterns = []
        sorted_numbers = sorted(set(all_numbers))
        
        for i in range(len(sorted_numbers) - 1):
            if sorted_numbers[i+1] - sorted_numbers[i] == 1:
                consecutive_patterns.extend([sorted_numbers[i], sorted_numbers[i+1]])
        
        # 选择一些连号作为杀号候选
        return list(set(consecutive_patterns))[:count]
    
    def _sum_analysis_kill_strategy(self, prev_periods: List[List[int]], 
                                  count: int) -> List[int]:
        """和值分析杀号策略"""
        if len(prev_periods) < 2:
            return []
        
        # 计算最近期的和值
        recent_sums = [sum(period) for period in prev_periods]
        avg_sum = sum(recent_sums) / len(recent_sums)
        
        # 根据和值趋势，杀掉一些极端号码
        if avg_sum < 80:  # 和值偏小，杀大号
            return list(range(30, 36))[:count]
        elif avg_sum > 120:  # 和值偏大，杀小号
            return list(range(1, 7))[:count]
        else:
            return []
    
    def _parity_kill_strategy(self, prev_periods: List[List[int]], 
                            count: int) -> List[int]:
        """奇偶分析杀号策略"""
        if len(prev_periods) < 2:
            return []
        
        # 分析奇偶比例
        odd_counts = []
        for period in prev_periods:
            odd_count = sum(1 for n in period if n % 2 == 1)
            odd_counts.append(odd_count)
        
        avg_odd = sum(odd_counts) / len(odd_counts)
        
        # 根据奇偶趋势杀号
        if avg_odd > 3:  # 奇数偏多，杀一些奇数
            odd_numbers = [n for n in range(1, 36) if n % 2 == 1]
            return odd_numbers[:count]
        elif avg_odd < 2:  # 偶数偏多，杀一些偶数
            even_numbers = [n for n in range(1, 36) if n % 2 == 0]
            return even_numbers[:count]
        else:
            return []
    
    def _consecutive_blue_kill_strategy(self, prev_periods: List[List[int]], 
                                      count: int) -> List[int]:
        """蓝球连号分析杀号策略"""
        if len(prev_periods) < 2:
            return []
        
        # 收集最近的蓝球号码
        all_blues = []
        for period in prev_periods:
            all_blues.extend(period)
        
        # 统计频率，选择高频号码作为杀号候选
        freq_counter = Counter(all_blues)
        most_common = freq_counter.most_common(count)
        
        return [num for num, _ in most_common]
    
    def _frequency_blue_kill_strategy(self, prev_periods: List[List[int]], 
                                    count: int) -> List[int]:
        """蓝球频率分析杀号策略"""
        if len(prev_periods) < 2:
            return []
        
        # 收集所有蓝球号码
        all_blues = []
        for period in prev_periods:
            all_blues.extend(period)
        
        # 统计频率，选择高频号码作为杀号候选
        freq_counter = Counter(all_blues)
        most_common = freq_counter.most_common(count)
        
        return [num for num, _ in most_common]
    
    def _parity_blue_kill_strategy(self, prev_periods: List[List[int]], 
                                 count: int) -> List[int]:
        """蓝球奇偶分析杀号策略"""
        if len(prev_periods) < 2:
            return []
        
        # 统计最近期的奇偶分布
        odd_count = 0
        even_count = 0
        
        for period in prev_periods[-3:]:  # 最近3期
            for num in period:
                if num % 2 == 0:
                    even_count += 1
                else:
                    odd_count += 1
        
        # 如果奇数过多，杀掉一些奇数；反之亦然
        kill_candidates = []
        if odd_count > even_count * 1.5:
            # 杀掉一些奇数
            odd_blues = [i for i in range(1, 17) if i % 2 == 1]
            kill_candidates = odd_blues[:count]
        elif even_count > odd_count * 1.5:
            # 杀掉一些偶数
            even_blues = [i for i in range(1, 17) if i % 2 == 0]
            kill_candidates = even_blues[:count]
        else:
            # 平衡状态，随机选择
            import random
            kill_candidates = random.sample(range(1, 17), count)
        
        return kill_candidates[:count]
    
    def _zone_blue_kill_strategy(self, prev_periods: List[List[int]], 
                               count: int) -> List[int]:
        """蓝球区间分析杀号策略"""
        if len(prev_periods) < 2:
            return []
        
        # 蓝球区间: 1-4(小), 5-8(小中), 9-12(中大), 13-16(大)
        zones = {
            'small': list(range(1, 5)),
            'small_mid': list(range(5, 9)),
            'mid_big': list(range(9, 13)),
            'big': list(range(13, 17))
        }
        
        # 统计各区间出现频率
        zone_counts = {zone: 0 for zone in zones}
        
        for period in prev_periods[-4:]:  # 最近4期
            for num in period:
                for zone_name, zone_range in zones.items():
                    if num in zone_range:
                        zone_counts[zone_name] += 1
                        break
        
        # 找出最活跃的区间，杀掉该区间的部分号码
        most_active_zone = max(zone_counts, key=zone_counts.get)
        if zone_counts[most_active_zone] > 0:
            zone_numbers = zones[most_active_zone]
            return zone_numbers[:count]
        
        return []
    
    def _random_blue_diversify_strategy(self, prev_periods: List[List[int]], 
                                      count: int) -> List[int]:
        """蓝球随机多样化策略"""
        import random
        
        # 收集最近出现的蓝球号码
        recent_blues = set()
        for period in prev_periods[-3:]:  # 最近3期
            recent_blues.update(period)
        
        # 从未出现的号码中随机选择
        all_blues = set(range(1, 17))
        unused_blues = list(all_blues - recent_blues)
        
        if len(unused_blues) >= count:
            return random.sample(unused_blues, count)
        else:
            # 如果未出现号码不够，随机选择
            return random.sample(list(all_blues), count)
    
    def _smart_merge_blue_strategies(self, strategy_kills: Dict[str, List[int]], 
                                   target_count: int) -> List[int]:
        """智能合并蓝球策略结果"""
        # 按权重收集杀号候选
        weighted_candidates = []
        
        for strategy, kills in strategy_kills.items():
            weight = self.strategy_weights.get(strategy, 0.1)
            for kill_num in kills:
                weighted_candidates.append((kill_num, weight))
        
        # 按权重和频率排序
        kill_scores = defaultdict(float)
        for num, weight in weighted_candidates:
            kill_scores[num] += weight
        
        # 选择得分最高的号码
        sorted_kills = sorted(kill_scores.items(), key=lambda x: x[1], reverse=True)
        final_kills = [num for num, _ in sorted_kills[:target_count]]
        
        # 确保数量足够
        if len(final_kills) < target_count:
            all_blues = set(range(1, 17))
            remaining = list(all_blues - set(final_kills))
            final_kills.extend(remaining[:target_count - len(final_kills)])
        
        return final_kills
    
    def _get_fallback_blue_kills(self, count: int) -> List[int]:
        """蓝球数据不足时的备用杀号策略"""
        # 使用经验杀号: 蓝球中较少出现的号码
        fallback_blues = [1, 2, 15, 16, 3, 14, 4, 13, 5, 12, 6, 11]
        return fallback_blues[:count]
    
    def _zone_analysis_kill_strategy(self, prev_periods: List[List[int]], 
                                   count: int) -> List[int]:
        """区间分析杀号策略 - 分析号码分布区间"""
        if len(prev_periods) < 3:
            return []
        
        # 定义区间: 1-7(小), 8-14(小中), 15-21(中), 22-28(中大), 29-35(大)
        zones = {
            'small': list(range(1, 8)),
            'small_mid': list(range(8, 15)),
            'mid': list(range(15, 22)),
            'mid_big': list(range(22, 29)),
            'big': list(range(29, 36))
        }
        
        # 统计各区间出现频率
        zone_counts = {zone: 0 for zone in zones}
        
        for period in prev_periods[-5:]:  # 最近5期
            for num in period:
                for zone_name, zone_range in zones.items():
                    if num in zone_range:
                        zone_counts[zone_name] += 1
                        break
        
        # 找出最活跃的区间，杀掉该区间的部分号码
        most_active_zone = max(zone_counts, key=zone_counts.get)
        if zone_counts[most_active_zone] > 0:
            zone_numbers = zones[most_active_zone]
            return zone_numbers[:count]
        
        return []
    
    def _gap_analysis_kill_strategy(self, prev_periods: List[List[int]], 
                                  count: int) -> List[int]:
        """间隔分析杀号策略 - 分析号码间隔模式"""
        if len(prev_periods) < 3:
            return []
        
        # 分析最近期的号码间隔
        recent_period = prev_periods[-1]
        sorted_numbers = sorted(recent_period)
        
        gaps = []
        for i in range(len(sorted_numbers) - 1):
            gap = sorted_numbers[i+1] - sorted_numbers[i]
            gaps.append(gap)
        
        # 如果间隔过于规律，杀掉可能的下一个规律号码
        kill_candidates = []
        
        # 检查是否有连续小间隔(1-2)
        small_gaps = [g for g in gaps if g <= 2]
        if len(small_gaps) >= 2:
            # 杀掉一些连续号码
            for num in sorted_numbers:
                if num + 1 in range(1, 36) and num + 1 not in recent_period:
                    kill_candidates.append(num + 1)
                if num + 2 in range(1, 36) and num + 2 not in recent_period:
                    kill_candidates.append(num + 2)
        
        # 检查是否有大间隔(>5)
        big_gaps = [g for g in gaps if g > 5]
        if len(big_gaps) >= 1:
            # 杀掉间隔中间的号码
            for i, gap in enumerate(gaps):
                if gap > 5:
                    start = sorted_numbers[i]
                    end = sorted_numbers[i+1]
                    mid_range = list(range(start + 2, end - 1))
                    kill_candidates.extend(mid_range[:2])
        
        return kill_candidates[:count]
    
    def _random_diversify_strategy(self, prev_periods: List[List[int]], 
                                 count: int) -> List[int]:
        """随机多样化策略 - 增加杀号随机性"""
        import random
        
        # 收集最近出现的号码
        recent_numbers = set()
        for period in prev_periods[-3:]:  # 最近3期
            recent_numbers.update(period)
        
        # 从未出现的号码中随机选择
        all_numbers = set(range(1, 36))
        unused_numbers = list(all_numbers - recent_numbers)
        
        if len(unused_numbers) >= count:
            return random.sample(unused_numbers, count)
        else:
            # 如果未出现号码不够，随机选择一些低频号码
            return random.sample(list(all_numbers), count)
    
    def _smart_merge_strategies(self, strategy_kills: Dict[str, List[int]], 
                              target_count: int) -> List[int]:
        """智能合并多策略结果"""
        # 按权重收集杀号候选
        weighted_candidates = []
        
        for strategy, kills in strategy_kills.items():
            weight = self.strategy_weights.get(strategy, 0.1)
            for kill_num in kills:
                weighted_candidates.append((kill_num, weight))
        
        # 按权重和频率排序
        kill_scores = defaultdict(float)
        for num, weight in weighted_candidates:
            kill_scores[num] += weight
        
        # 选择得分最高的号码
        sorted_kills = sorted(kill_scores.items(), key=lambda x: x[1], reverse=True)
        final_kills = [num for num, _ in sorted_kills[:target_count]]
        
        # 确保数量足够
        if len(final_kills) < target_count:
            all_numbers = set(range(1, 36))
            remaining = list(all_numbers - set(final_kills))
            final_kills.extend(remaining[:target_count - len(final_kills)])
        
        return final_kills
    
    def _get_fallback_red_kills(self, count: int) -> List[int]:
        """数据不足时的备用杀号策略"""
        # 使用经验杀号: 通常较少出现的号码
        fallback_numbers = [1, 2, 34, 35, 7, 14, 21, 28, 3, 6, 9, 12, 15, 18]
        return fallback_numbers[:count]
    
    def update_strategy_performance(self, period_result: List[int]):
        """更新策略性能 - 用于动态调整权重"""
        if not self.red_kill_history:
            return
        
        last_kill_record = self.red_kill_history[-1]
        killed_numbers = last_kill_record['kills']
        
        # 检查杀号成功率 (杀号中没有中奖号码才算成功)
        success = not any(num in period_result for num in killed_numbers)
        
        # 更新各策略性能 (简化版，实际可以更精细)
        for strategy in last_kill_record['strategies_used']:
            if strategy in self.strategy_performance:
                self.strategy_performance[strategy]['total'] += 1
                if success:
                    self.strategy_performance[strategy]['hits'] += 1
                
                # 更新成功率
                perf = self.strategy_performance[strategy]
                perf['success_rate'] = perf['hits'] / perf['total'] if perf['total'] > 0 else 0.0
        
        # 动态调整权重 (每10次更新一次)
        if sum(p['total'] for p in self.strategy_performance.values()) % 10 == 0:
            self._adjust_strategy_weights()
    
    def _adjust_strategy_weights(self):
        """动态调整策略权重"""
        total_weight = 0
        
        for strategy, perf in self.strategy_performance.items():
            if perf['total'] >= 5:  # 至少有5次记录才调整
                # 基于成功率调整权重
                base_weight = 1.0 / len(self.strategy_weights)  # 基础权重
                performance_factor = max(0.5, min(2.0, perf['success_rate'] * 2))  # 性能因子
                new_weight = base_weight * performance_factor
                self.strategy_weights[strategy] = new_weight
                total_weight += new_weight
        
        # 归一化权重
        if total_weight > 0:
            for strategy in self.strategy_weights:
                self.strategy_weights[strategy] /= total_weight


# 向后兼容的类别名
NumberKiller = UnifiedKillAlgorithms
UniversalKiller = UnifiedKillAlgorithms
BlueKiller = UnifiedKillAlgorithms
