#!/usr/bin/env python3
"""
基于回测准确率的ML系统优化
根据实际回测结果优化特征工程和集成权重
"""

import sys
import os
import time
import json
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Tuple, Any
from collections import defaultdict

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def load_lottery_data():
    """加载彩票数据"""
    try:
        # 尝试多个可能的数据文件位置
        possible_files = [
            project_root / "data" / "raw" / "dlt_data.csv",
            project_root / "data" / "dlt_history.csv",
            project_root / "data" / "processed" / "dlt_data.csv",
        ]
        
        for data_file in possible_files:
            if data_file.exists():
                data = pd.read_csv(data_file)
                print(f"✅ 加载数据成功: {len(data)} 期 (文件: {data_file})")
                return data
        
        print(f"❌ 数据文件不存在")
        return None
        
    except Exception as e:
        print(f"❌ 加载数据失败: {e}")
        return None

def run_backtest_evaluation(periods: int = 20) -> Dict[str, Any]:
    """运行回测评估"""
    print(f"\n🧪 运行回测评估 ({periods} 期)...")
    
    try:
        from src.systems.main import LotteryPredictor
        
        # 加载数据
        data = load_lottery_data()
        if data is None:
            return None
        
        # 创建系统
        system = LotteryPredictor()
        
        # 回测结果统计
        results = {
            'total_periods': 0,
            'ratio_accuracy': {
                'red_odd_even': {'correct': 0, 'total': 0},
                'red_size': {'correct': 0, 'total': 0},
                'blue_size': {'correct': 0, 'total': 0}
            },
            'kill_success': {
                'red_kill_success': 0,
                'blue_kill_success': 0,
                'total_periods': 0
            },
            'number_hits': {
                'red_hits': defaultdict(int),
                'blue_hits': defaultdict(int),
                'hit_2_plus_1': 0
            },
            'period_details': []
        }
        
        # 选择最近的期号进行回测
        recent_periods = data.tail(periods + 5)  # 多取5期作为缓冲
        
        for i in range(len(recent_periods) - 1):
            try:
                current_row = recent_periods.iloc[i]
                next_row = recent_periods.iloc[i + 1]
                
                period = int(current_row['期号'])
                next_period = period + 1
                
                # 预测下一期
                prediction = system.predict_next_period(next_period)
                
                # 获取实际开奖号码
                actual_red = [int(next_row[f'红球{j}']) for j in range(1, 6)]
                actual_blue = [int(next_row[f'蓝球{j}']) for j in range(1, 3)]

                # 计算实际比例
                actual_red_odd_count = sum(1 for x in actual_red if x % 2 == 1)
                actual_red_even_count = 5 - actual_red_odd_count
                actual_red_odd_even = f"{actual_red_odd_count}:{actual_red_even_count}"

                actual_red_big_count = sum(1 for x in actual_red if x > 18)
                actual_red_small_count = 5 - actual_red_big_count
                actual_red_size = f"{actual_red_big_count}:{actual_red_small_count}"

                actual_blue_big_count = sum(1 for x in actual_blue if x > 6)
                actual_blue_small_count = 2 - actual_blue_big_count
                actual_blue_size = f"{actual_blue_big_count}:{actual_blue_small_count}"

                # 从预测号码中计算预测比例
                pred_numbers = prediction.get('generated_numbers', ([], []))
                if pred_numbers and len(pred_numbers) == 2:
                    pred_red, pred_blue = pred_numbers

                    # 计算预测比例
                    if pred_red:
                        pred_red_odd_count = sum(1 for x in pred_red if x % 2 == 1)
                        pred_red_even_count = len(pred_red) - pred_red_odd_count
                        pred_red_odd_even = f"{pred_red_odd_count}:{pred_red_even_count}"

                        pred_red_big_count = sum(1 for x in pred_red if x > 18)
                        pred_red_small_count = len(pred_red) - pred_red_big_count
                        pred_red_size = f"{pred_red_big_count}:{pred_red_small_count}"
                    else:
                        pred_red_odd_even = "0:0"
                        pred_red_size = "0:0"

                    if pred_blue:
                        pred_blue_big_count = sum(1 for x in pred_blue if x > 6)
                        pred_blue_small_count = len(pred_blue) - pred_blue_big_count
                        pred_blue_size = f"{pred_blue_big_count}:{pred_blue_small_count}"
                    else:
                        pred_blue_size = "0:0"
                else:
                    pred_red_odd_even = "0:0"
                    pred_red_size = "0:0"
                    pred_blue_size = "0:0"
                
                red_odd_even_hit = pred_red_odd_even == actual_red_odd_even
                red_size_hit = pred_red_size == actual_red_size
                blue_size_hit = pred_blue_size == actual_blue_size
                
                # 更新比例准确率统计
                results['ratio_accuracy']['red_odd_even']['total'] += 1
                results['ratio_accuracy']['red_size']['total'] += 1
                results['ratio_accuracy']['blue_size']['total'] += 1
                
                if red_odd_even_hit:
                    results['ratio_accuracy']['red_odd_even']['correct'] += 1
                if red_size_hit:
                    results['ratio_accuracy']['red_size']['correct'] += 1
                if blue_size_hit:
                    results['ratio_accuracy']['blue_size']['correct'] += 1
                
                # 评估杀号成功率
                kill_numbers = prediction.get('kill_numbers', {})
                red_kills = kill_numbers.get('red', [])
                blue_kills = kill_numbers.get('blue', [])
                
                red_kill_success = len(set(red_kills) & set(actual_red)) == 0 if red_kills else True
                blue_kill_success = len(set(blue_kills) & set(actual_blue)) == 0 if blue_kills else True
                
                if red_kill_success:
                    results['kill_success']['red_kill_success'] += 1
                if blue_kill_success:
                    results['kill_success']['blue_kill_success'] += 1
                results['kill_success']['total_periods'] += 1
                
                # 评估号码命中率
                if pred_numbers and len(pred_numbers) == 2:
                    pred_red_nums, pred_blue_nums = pred_numbers

                    red_hit_count = len(set(pred_red_nums) & set(actual_red))
                    blue_hit_count = len(set(pred_blue_nums) & set(actual_blue))

                    results['number_hits']['red_hits'][red_hit_count] += 1
                    results['number_hits']['blue_hits'][blue_hit_count] += 1

                    # 检查2+1命中（至少2个红球+1个蓝球）
                    if red_hit_count >= 2 and blue_hit_count >= 1:
                        results['number_hits']['hit_2_plus_1'] += 1
                
                # 记录期号详情
                period_detail = {
                    'period': period,
                    'predictions': {
                        'red_odd_even': pred_red_odd_even,
                        'red_size': pred_red_size,
                        'blue_size': pred_blue_size
                    },
                    'actuals': {
                        'red_odd_even': actual_red_odd_even,
                        'red_size': actual_red_size,
                        'blue_size': actual_blue_size
                    },
                    'hits': {
                        'red_odd_even': red_odd_even_hit,
                        'red_size': red_size_hit,
                        'blue_size': blue_size_hit
                    },
                    'kill_success': {
                        'red': red_kill_success,
                        'blue': blue_kill_success
                    }
                }
                results['period_details'].append(period_detail)
                results['total_periods'] += 1
                
                print(f"  期号 {period}: 奇偶{'✅' if red_odd_even_hit else '❌'} 大小{'✅' if red_size_hit else '❌'} 蓝球{'✅' if blue_size_hit else '❌'}")
                
            except Exception as e:
                print(f"  ⚠️ 期号 {period} 回测失败: {e}")
                continue
        
        return results
        
    except Exception as e:
        print(f"❌ 回测评估失败: {e}")
        return None

def analyze_accuracy_results(results: Dict[str, Any]) -> Dict[str, Any]:
    """分析准确率结果"""
    print("\n📊 分析准确率结果...")
    
    if not results or results['total_periods'] == 0:
        print("❌ 没有有效的回测结果")
        return None
    
    total_periods = results['total_periods']
    analysis = {
        'overall_performance': {},
        'ratio_performance': {},
        'kill_performance': {},
        'number_performance': {},
        'optimization_recommendations': []
    }
    
    # 分析比例预测性能
    ratio_acc = results['ratio_accuracy']
    for ratio_type, stats in ratio_acc.items():
        if stats['total'] > 0:
            accuracy = stats['correct'] / stats['total']
            analysis['ratio_performance'][ratio_type] = {
                'accuracy': accuracy,
                'correct': stats['correct'],
                'total': stats['total'],
                'percentage': f"{accuracy:.1%}"
            }
    
    # 分析杀号性能
    kill_stats = results['kill_success']
    if kill_stats['total_periods'] > 0:
        red_kill_rate = kill_stats['red_kill_success'] / kill_stats['total_periods']
        blue_kill_rate = kill_stats['blue_kill_success'] / kill_stats['total_periods']
        
        analysis['kill_performance'] = {
            'red_kill_success_rate': red_kill_rate,
            'blue_kill_success_rate': blue_kill_rate,
            'red_percentage': f"{red_kill_rate:.1%}",
            'blue_percentage': f"{blue_kill_rate:.1%}"
        }
    
    # 分析号码命中性能
    number_stats = results['number_hits']
    hit_2_plus_1_rate = number_stats['hit_2_plus_1'] / total_periods if total_periods > 0 else 0
    
    analysis['number_performance'] = {
        'hit_2_plus_1_rate': hit_2_plus_1_rate,
        'hit_2_plus_1_percentage': f"{hit_2_plus_1_rate:.1%}",
        'red_hit_distribution': dict(number_stats['red_hits']),
        'blue_hit_distribution': dict(number_stats['blue_hits'])
    }
    
    # 计算总体性能评分
    ratio_scores = [perf['accuracy'] for perf in analysis['ratio_performance'].values()]
    avg_ratio_accuracy = np.mean(ratio_scores) if ratio_scores else 0
    
    kill_scores = [analysis['kill_performance'].get('red_kill_success_rate', 0),
                   analysis['kill_performance'].get('blue_kill_success_rate', 0)]
    avg_kill_success = np.mean(kill_scores)
    
    overall_score = 0.6 * avg_ratio_accuracy + 0.3 * avg_kill_success + 0.1 * hit_2_plus_1_rate
    
    analysis['overall_performance'] = {
        'overall_score': overall_score,
        'avg_ratio_accuracy': avg_ratio_accuracy,
        'avg_kill_success': avg_kill_success,
        'hit_2_plus_1_rate': hit_2_plus_1_rate,
        'grade': get_performance_grade(overall_score)
    }
    
    # 生成优化建议
    analysis['optimization_recommendations'] = generate_optimization_recommendations(analysis)
    
    return analysis

def get_performance_grade(score: float) -> str:
    """获取性能等级"""
    if score >= 0.8:
        return "优秀 (A)"
    elif score >= 0.7:
        return "良好 (B)"
    elif score >= 0.6:
        return "中等 (C)"
    elif score >= 0.5:
        return "及格 (D)"
    else:
        return "不及格 (F)"

def generate_optimization_recommendations(analysis: Dict[str, Any]) -> List[str]:
    """生成优化建议"""
    recommendations = []
    
    ratio_perf = analysis['ratio_performance']
    kill_perf = analysis['kill_performance']
    overall = analysis['overall_performance']
    
    # 基于比例预测性能的建议
    for ratio_type, perf in ratio_perf.items():
        if perf['accuracy'] < 0.6:
            if ratio_type == 'red_odd_even':
                recommendations.append(f"🔧 红球奇偶比预测准确率偏低({perf['percentage']})，建议增强奇偶比预测算法权重")
            elif ratio_type == 'red_size':
                recommendations.append(f"🔧 红球大小比预测准确率偏低({perf['percentage']})，建议优化大小比特征工程")
            elif ratio_type == 'blue_size':
                recommendations.append(f"🔧 蓝球大小比预测准确率偏低({perf['percentage']})，建议增强蓝球预测模型")
    
    # 基于杀号性能的建议
    if kill_perf.get('red_kill_success_rate', 0) < 0.8:
        recommendations.append(f"🎯 红球杀号成功率偏低({kill_perf.get('red_percentage', 'N/A')})，建议优化红球杀号算法")
    
    if kill_perf.get('blue_kill_success_rate', 0) < 0.8:
        recommendations.append(f"🎯 蓝球杀号成功率偏低({kill_perf.get('blue_percentage', 'N/A')})，建议优化蓝球杀号算法")
    
    # 基于整体性能的建议
    if overall['overall_score'] < 0.7:
        recommendations.append("📈 整体性能需要提升，建议全面优化ML模型和集成权重")
        
        if overall['avg_ratio_accuracy'] < 0.6:
            recommendations.append("🎯 比例预测是主要瓶颈，建议重点优化特征工程和ML模型")
        
        if overall['avg_kill_success'] < 0.8:
            recommendations.append("🔪 杀号算法需要改进，建议调整杀号策略和参数")
    
    # ML权重优化建议
    if overall['avg_ratio_accuracy'] > 0.7:
        recommendations.append("⚖️ 比例预测表现良好，建议提高ML预测器权重至3.5")
    elif overall['avg_ratio_accuracy'] < 0.5:
        recommendations.append("⚖️ 比例预测表现不佳，建议降低ML预测器权重至2.0")
    
    return recommendations

def main():
    """主函数"""
    print("🎯 基于回测准确率的ML系统优化")
    print("=" * 60)
    
    # 1. 运行回测评估
    print("\n📊 第一步: 运行回测评估")
    backtest_results = run_backtest_evaluation(periods=30)  # 回测30期
    
    if backtest_results is None:
        print("❌ 回测评估失败，无法继续优化")
        return
    
    print("✅ 回测评估完成")
    
    # 2. 分析准确率结果
    print("\n🔍 第二步: 分析准确率结果")
    accuracy_analysis = analyze_accuracy_results(backtest_results)
    
    if accuracy_analysis is None:
        print("❌ 准确率分析失败")
        return
    
    print("✅ 准确率分析完成")
    
    # 3. 显示分析结果
    print("\n📈 第三步: 准确率分析报告")
    display_accuracy_report(accuracy_analysis)
    
    # 4. 保存结果
    print("\n💾 第四步: 保存分析结果")
    save_accuracy_analysis(backtest_results, accuracy_analysis)
    
    print("\n🎉 基于准确率的优化分析完成!")

def display_accuracy_report(analysis: Dict[str, Any]):
    """显示准确率报告"""
    overall = analysis['overall_performance']
    ratio_perf = analysis['ratio_performance']
    kill_perf = analysis['kill_performance']
    number_perf = analysis['number_performance']
    
    print(f"\n📊 整体性能评估:")
    print(f"  总体评分: {overall['overall_score']:.3f} - {overall['grade']}")
    print(f"  平均比例准确率: {overall['avg_ratio_accuracy']:.1%}")
    print(f"  平均杀号成功率: {overall['avg_kill_success']:.1%}")
    print(f"  2+1命中率: {overall['hit_2_plus_1_rate']:.1%}")
    
    print(f"\n🎯 比例预测性能:")
    for ratio_type, perf in ratio_perf.items():
        status = "✅" if perf['accuracy'] >= 0.6 else "❌"
        print(f"  {ratio_type}: {perf['percentage']} ({perf['correct']}/{perf['total']}) {status}")
    
    print(f"\n🔪 杀号性能:")
    print(f"  红球杀号成功率: {kill_perf.get('red_percentage', 'N/A')}")
    print(f"  蓝球杀号成功率: {kill_perf.get('blue_percentage', 'N/A')}")
    
    print(f"\n🎲 号码命中性能:")
    print(f"  2+1命中率: {number_perf['hit_2_plus_1_percentage']}")
    
    print(f"\n💡 优化建议:")
    for i, recommendation in enumerate(analysis['optimization_recommendations'], 1):
        print(f"  {i}. {recommendation}")

def save_accuracy_analysis(backtest_results: Dict, analysis: Dict):
    """保存准确率分析结果"""
    results_dir = Path("optimization_results")
    results_dir.mkdir(exist_ok=True)
    
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    
    # 保存回测结果
    backtest_file = results_dir / f"backtest_results_{timestamp}.json"
    with open(backtest_file, 'w', encoding='utf-8') as f:
        json.dump(backtest_results, f, ensure_ascii=False, indent=2, default=str)
    
    # 保存分析结果
    analysis_file = results_dir / f"accuracy_analysis_{timestamp}.json"
    with open(analysis_file, 'w', encoding='utf-8') as f:
        json.dump(analysis, f, ensure_ascii=False, indent=2, default=str)
    
    print(f"📁 回测结果已保存: {backtest_file}")
    print(f"📁 分析结果已保存: {analysis_file}")

if __name__ == "__main__":
    main()
