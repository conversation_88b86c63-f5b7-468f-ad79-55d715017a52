#!/usr/bin/env python3
"""
大乐透预测系统主程序
使用重构后的项目结构
"""

import sys
from pathlib import Path

# 添加src目录到Python路径 - 修正路径
src_path = Path(__file__).parent.parent
sys.path.insert(0, str(src_path))


def main():
    """主函数"""
    try:
        # 导入基础系统
        from systems.main import LotteryPredictor

        print("🎯 大乐透预测系统")
        print("=" * 50)

        # 创建预测器
        predictor = LotteryPredictor()

        # 默认运行：历史回测验证 + 预测下一期
        print("📊 历史回测验证")
        print("=" * 60)
        predictor.run_backtest(num_periods=10, display_periods=5)

        print("\n" + "=" * 60)
        predict_next_period(predictor)

    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("💡 提示: 请确保已安装必要的依赖包")
        print("   pip install pandas numpy scikit-learn")
    except Exception as e:
        print(f"❌ 运行错误: {e}")
        import traceback

        traceback.print_exc()


def predict_next_period(predictor):
    """预测下一期号码"""
    from datetime import datetime
    from utils.utils import format_numbers

    print("\n" + "=" * 60)
    print("🔮 预测下一期开奖号码")
    print("=" * 60)

    # 获取最新期号
    latest_period = predictor.data.iloc[0]["期号"]
    print(f"📅 基于最新期号: {latest_period}")
    print(f"🕐 预测时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # 预测下一期（使用索引0，即最新一期的数据来预测下一期）
    prediction = predictor.predict_next_period(0)

    # 显示预测结果
    print(f"\n🎯 预测下一期号码:")

    # 显示比例预测
    if "predictions" in prediction:
        preds = prediction["predictions"]
        print(f"\n📊 比例预测:")

        # 红球奇偶比
        if "red_odd_even" in preds:
            red_oe = preds["red_odd_even"]
            if isinstance(red_oe, list) and len(red_oe) >= 2:
                main_pred, alt_pred = red_oe[0], red_oe[1]
                print(
                    f"  红球奇偶比: {main_pred[0]} (置信度: {main_pred[1]:.3f}) | 备选: {alt_pred[0]} ({alt_pred[1]:.3f})"
                )
            else:
                print(f"  红球奇偶比: {red_oe}")

        # 红球大小比
        if "red_size" in preds:
            red_size = preds["red_size"]
            if isinstance(red_size, list) and len(red_size) >= 2:
                main_pred, alt_pred = red_size[0], red_size[1]
                print(
                    f"  红球大小比: {main_pred[0]} (置信度: {main_pred[1]:.3f}) | 备选: {alt_pred[0]} ({alt_pred[1]:.3f})"
                )
            else:
                print(f"  红球大小比: {red_size}")

        # 蓝球大小比
        if "blue_size" in preds:
            blue_size = preds["blue_size"]
            if isinstance(blue_size, list) and len(blue_size) >= 2:
                main_pred, alt_pred = blue_size[0], blue_size[1]
                print(
                    f"  蓝球大小比: {main_pred[0]} (置信度: {main_pred[1]:.3f}) | 备选: {alt_pred[0]} ({alt_pred[1]:.3f})"
                )
            else:
                print(f"  蓝球大小比: {blue_size}")

    # 显示杀号
    if "kill_numbers" in prediction:
        kill_info = prediction["kill_numbers"]
        if "red" in kill_info and kill_info["red"]:
            red_kills = [
                num
                for sublist in kill_info["red"]
                for num in sublist
                if isinstance(sublist, list)
            ]
            if red_kills:
                print(f"\n🚫 杀号预测:")
                print(f"  红球杀号: {format_numbers(red_kills[:5])}")

        if "blue" in kill_info and kill_info["blue"]:
            blue_kills = [
                num
                for sublist in kill_info["blue"]
                for num in sublist
                if isinstance(sublist, list)
            ]
            if blue_kills:
                print(f"  蓝球杀号: {format_numbers(blue_kills[:2])}")

    # 显示号码推荐（如果有的话）
    if "generated_numbers" in prediction:
        pred_red, pred_blue = prediction["generated_numbers"]
        print(f"\n⭐ 推荐号码: {format_numbers(pred_red)}——{format_numbers(pred_blue)}")

    if "bayes_selected" in prediction and prediction["bayes_selected"]:
        print(f"\n🎯 贝叶斯推荐组合:")
        for i, combo in enumerate(prediction["bayes_selected"][:5], 1):
            red_str = format_numbers(combo["red_balls"])
            blue_str = format_numbers(combo["blue_balls"])
            confidence = combo.get("confidence", 0)
            recommendation = combo.get("recommendation", "")
            print(f"  第{i}名：{red_str}——{blue_str} {recommendation} ({confidence}%)")

    print(f"\n💡 提示: 以上预测仅供参考，请理性购彩！")


if __name__ == "__main__":
    main()
