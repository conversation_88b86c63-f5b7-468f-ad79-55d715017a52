#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的比例预测器 - 解决固化预测问题
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple
import random

class EnhancedRatioPredictor:
    """增强的比例预测器 - 多样化策略"""
    
    def __init__(self):
        self.strategies = {
            "historical_frequency": 0.3,
            "trend_analysis": 0.25,
            "cycle_detection": 0.2,
            "anti_pattern": 0.15,
            "random_diversity": 0.1
        }
        self.anti_streak_threshold = 3
        self.max_confidence = 0.8  # 降低过度自信
        self.min_confidence = 0.3
        
    def predict_red_odd_even_ratio(self, data: pd.DataFrame, current_period: int) -> Tuple[str, float]:
        """预测红球奇偶比 - 多样化策略"""
        if len(data) < 10:
            return "3:2", 0.5
            
        # 计算历史比例分布
        recent_data = data.tail(50)
        ratio_counts = {}
        
        for _, row in recent_data.iterrows():
            red_balls = [row[f'红球{i}'] for i in range(1, 6)]
            odd_count = sum(1 for x in red_balls if x % 2 == 1)
            ratio = f"{odd_count}:{5-odd_count}"
            ratio_counts[ratio] = ratio_counts.get(ratio, 0) + 1
        
        # 策略1: 历史频率 (30%)
        most_frequent = max(ratio_counts, key=ratio_counts.get) if ratio_counts else "3:2"
        
        # 策略2: 趋势分析 (25%)
        recent_5 = data.tail(5)
        trend_ratios = []
        for _, row in recent_5.iterrows():
            red_balls = [row[f'红球{i}'] for i in range(1, 6)]
            odd_count = sum(1 for x in red_balls if x % 2 == 1)
            trend_ratios.append(odd_count)
        
        trend_avg = np.mean(trend_ratios) if trend_ratios else 2.5
        trend_prediction = f"{int(round(trend_avg))}:{5-int(round(trend_avg))}"
        
        # 策略3: 反模式 (15%) - 避免连续相同预测
        last_3_ratios = []
        for _, row in data.tail(3).iterrows():
            red_balls = [row[f'红球{i}'] for i in range(1, 6)]
            odd_count = sum(1 for x in red_balls if x % 2 == 1)
            last_3_ratios.append(f"{odd_count}:{5-odd_count}")
        
        # 如果最近3期都是同一比例，倾向于选择不同的
        if len(set(last_3_ratios)) == 1 and len(last_3_ratios) >= 3:
            all_ratios = ["0:5", "1:4", "2:3", "3:2", "4:1", "5:0"]
            anti_pattern_prediction = random.choice([r for r in all_ratios if r != last_3_ratios[0]])
        else:
            anti_pattern_prediction = most_frequent
        
        # 策略4: 随机多样性 (10%)
        all_ratios = ["1:4", "2:3", "3:2", "4:1"]  # 常见比例
        random_prediction = random.choice(all_ratios)
        
        # 综合决策
        predictions = [most_frequent, trend_prediction, anti_pattern_prediction, random_prediction]
        weights = [0.3, 0.25, 0.15, 0.1]
        
        # 加权随机选择
        final_prediction = np.random.choice(predictions, p=weights/np.sum(weights))
        
        # 计算置信度 (降低过度自信)
        consistency = predictions.count(final_prediction) / len(predictions)
        confidence = self.min_confidence + (self.max_confidence - self.min_confidence) * consistency
        confidence = min(confidence, self.max_confidence)
        
        return final_prediction, confidence
    
    def predict_red_size_ratio(self, data: pd.DataFrame, current_period: int) -> Tuple[str, float]:
        """预测红球大小比 - 类似的多样化策略"""
        if len(data) < 10:
            return "2:3", 0.5
            
        # 使用类似的多样化策略
        recent_data = data.tail(50)
        ratio_counts = {}
        
        for _, row in recent_data.iterrows():
            red_balls = [row[f'红球{i}'] for i in range(1, 6)]
            big_count = sum(1 for x in red_balls if x > 18)
            ratio = f"{big_count}:{5-big_count}"
            ratio_counts[ratio] = ratio_counts.get(ratio, 0) + 1
        
        # 应用多样化策略
        most_frequent = max(ratio_counts, key=ratio_counts.get) if ratio_counts else "2:3"
        
        # 添加随机性避免固化
        all_ratios = ["1:4", "2:3", "3:2", "4:1"]
        if random.random() < 0.2:  # 20%概率选择随机比例
            final_prediction = random.choice(all_ratios)
            confidence = 0.4
        else:
            final_prediction = most_frequent
            confidence = min(0.7, self.max_confidence)
        
        return final_prediction, confidence
    
    def predict_blue_size_ratio(self, data: pd.DataFrame, current_period: int) -> Tuple[str, float]:
        """预测蓝球大小比 - 多样化策略"""
        if len(data) < 10:
            return "1:1", 0.5
            
        recent_data = data.tail(30)
        ratio_counts = {}
        
        for _, row in recent_data.iterrows():
            blue_balls = [row[f'蓝球{i}'] for i in range(1, 3)]
            big_count = sum(1 for x in blue_balls if x > 6)
            ratio = f"{big_count}:{2-big_count}"
            ratio_counts[ratio] = ratio_counts.get(ratio, 0) + 1
        
        most_frequent = max(ratio_counts, key=ratio_counts.get) if ratio_counts else "1:1"
        
        # 蓝球预测添加更多随机性
        all_ratios = ["0:2", "1:1", "2:0"]
        if random.random() < 0.3:  # 30%概率随机选择
            final_prediction = random.choice(all_ratios)
            confidence = 0.4
        else:
            final_prediction = most_frequent
            confidence = min(0.6, self.max_confidence)
        
        return final_prediction, confidence
