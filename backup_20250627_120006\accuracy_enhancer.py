#!/usr/bin/env python3
"""
预测准确率提升优化器
Prediction Accuracy Enhancement Optimizer

通过集成学习、模型融合、动态权重调整等技术
将杀号成功率从当前水平提升到95%以上
"""

import os
import json
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Tuple, Optional, Callable
from dataclasses import dataclass, field
from pathlib import Path
import time
from collections import defaultdict, deque
import warnings
warnings.filterwarnings('ignore')


@dataclass
class AccuracyConfig:
    """准确率提升配置"""
    # 目标准确率
    target_kill_success_rate: float = 0.95
    target_prediction_accuracy: float = 0.90
    target_hit_2_plus_1_rate: float = 0.70
    
    # 集成学习参数
    ensemble_size: int = 5
    ensemble_method: str = 'weighted_voting'  # 'weighted_voting', 'stacking', 'blending'
    
    # 动态权重调整
    weight_adaptation_rate: float = 0.1
    performance_window: int = 20
    min_weight: float = 0.05
    max_weight: float = 0.8
    
    # 模型融合参数
    fusion_method: str = 'adaptive'  # 'simple', 'weighted', 'adaptive', 'meta_learning'
    confidence_threshold: float = 0.6
    
    # 在线学习参数
    online_learning: bool = True
    learning_rate: float = 0.01
    forgetting_factor: float = 0.95
    
    # 多样性促进
    diversity_penalty: float = 0.1
    diversity_threshold: float = 0.3


class ModelPerformanceTracker:
    """模型性能跟踪器"""
    
    def __init__(self, window_size: int = 50):
        """
        初始化性能跟踪器
        
        Args:
            window_size: 性能窗口大小
        """
        self.window_size = window_size
        self.performance_history = defaultdict(lambda: deque(maxlen=window_size))
        self.weights = defaultdict(float)
        self.total_predictions = defaultdict(int)
        self.correct_predictions = defaultdict(int)
        self.logger = logging.getLogger(f"lottery_predictor.{self.__class__.__name__}")
    
    def update_performance(self, model_name: str, prediction_correct: bool, confidence: float = 1.0):
        """
        更新模型性能
        
        Args:
            model_name: 模型名称
            prediction_correct: 预测是否正确
            confidence: 预测置信度
        """
        # 记录性能
        self.performance_history[model_name].append({
            'correct': prediction_correct,
            'confidence': confidence,
            'timestamp': time.time()
        })
        
        # 更新统计
        self.total_predictions[model_name] += 1
        if prediction_correct:
            self.correct_predictions[model_name] += 1
        
        # 更新权重
        self._update_weights()
    
    def _update_weights(self):
        """更新模型权重"""
        total_weight = 0.0
        
        for model_name in self.performance_history:
            if len(self.performance_history[model_name]) > 0:
                # 计算近期准确率
                recent_performance = list(self.performance_history[model_name])
                correct_count = sum(1 for p in recent_performance if p['correct'])
                accuracy = correct_count / len(recent_performance)
                
                # 计算置信度加权准确率
                weighted_accuracy = sum(
                    p['confidence'] if p['correct'] else 0
                    for p in recent_performance
                ) / len(recent_performance)
                
                # 综合权重
                weight = 0.7 * accuracy + 0.3 * weighted_accuracy
                self.weights[model_name] = weight
                total_weight += weight
        
        # 归一化权重
        if total_weight > 0:
            for model_name in self.weights:
                self.weights[model_name] /= total_weight
    
    def get_model_weights(self) -> Dict[str, float]:
        """获取模型权重"""
        return dict(self.weights)
    
    def get_model_accuracy(self, model_name: str) -> float:
        """获取模型准确率"""
        if model_name not in self.performance_history:
            return 0.0
        
        recent_performance = list(self.performance_history[model_name])
        if not recent_performance:
            return 0.0
        
        correct_count = sum(1 for p in recent_performance if p['correct'])
        return correct_count / len(recent_performance)
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能总结"""
        summary = {}
        
        for model_name in self.performance_history:
            accuracy = self.get_model_accuracy(model_name)
            weight = self.weights.get(model_name, 0.0)
            total = self.total_predictions.get(model_name, 0)
            correct = self.correct_predictions.get(model_name, 0)
            
            summary[model_name] = {
                'recent_accuracy': accuracy,
                'weight': weight,
                'total_predictions': total,
                'correct_predictions': correct,
                'overall_accuracy': correct / total if total > 0 else 0.0
            }
        
        return summary


class EnsemblePredictor:
    """集成预测器"""
    
    def __init__(self, config: AccuracyConfig = None):
        """
        初始化集成预测器
        
        Args:
            config: 准确率提升配置
        """
        self.config = config or AccuracyConfig()
        self.logger = logging.getLogger(f"lottery_predictor.{self.__class__.__name__}")
        
        # 性能跟踪器
        self.performance_tracker = ModelPerformanceTracker(self.config.performance_window)
        
        # 预测器列表
        self.predictors = {}
        self.predictor_types = [
            'enhanced_kill_system',
            'enhanced_bayesian',
            'optimized_markov',
            'lstm_predictor',
            'transformer_predictor',
            'multi_task_neural'
        ]
        
        # 融合历史
        self.fusion_history = deque(maxlen=100)
        
        # 在线学习参数
        self.online_weights = defaultdict(float)
        self.adaptation_count = 0
    
    def initialize_predictors(self):
        """初始化预测器"""
        try:
            from src.core.predictor_factory import ConfigurablePredictorFactory
            
            factory = ConfigurablePredictorFactory()
            
            for predictor_type in self.predictor_types:
                try:
                    predictor = factory.create_predictor(predictor_type)
                    self.predictors[predictor_type] = predictor
                    self.online_weights[predictor_type] = 1.0 / len(self.predictor_types)
                    self.logger.info(f"初始化预测器: {predictor_type}")
                except Exception as e:
                    self.logger.warning(f"无法初始化预测器 {predictor_type}: {e}")
            
            self.logger.info(f"成功初始化 {len(self.predictors)} 个预测器")
            
        except Exception as e:
            self.logger.error(f"预测器初始化失败: {e}")
    
    def ensemble_predict(self, data, period_index: int) -> Dict[str, Any]:
        """
        集成预测
        
        Args:
            data: 历史数据
            period_index: 期号索引
            
        Returns:
            集成预测结果
        """
        if not self.predictors:
            self.initialize_predictors()
        
        # 收集各预测器的预测结果
        predictions = {}
        confidences = {}
        
        for predictor_name, predictor in self.predictors.items():
            try:
                # 获取预测结果
                if hasattr(predictor, 'predict'):
                    result = predictor.predict(data, period_index)
                    
                    if hasattr(result, 'metadata'):
                        predictions[predictor_name] = result.metadata
                        confidences[predictor_name] = result.metadata.get('confidence', 0.5)
                    else:
                        predictions[predictor_name] = result
                        confidences[predictor_name] = 0.5
                
            except Exception as e:
                self.logger.warning(f"预测器 {predictor_name} 预测失败: {e}")
                continue
        
        # 执行集成融合
        ensemble_result = self._fuse_predictions(predictions, confidences)
        
        return ensemble_result
    
    def _fuse_predictions(self, predictions: Dict[str, Any], confidences: Dict[str, float]) -> Dict[str, Any]:
        """
        融合预测结果
        
        Args:
            predictions: 各预测器的预测结果
            confidences: 各预测器的置信度
            
        Returns:
            融合后的预测结果
        """
        if not predictions:
            return {'error': '没有可用的预测结果'}
        
        # 获取当前权重
        current_weights = self.performance_tracker.get_model_weights()
        
        # 如果没有历史权重，使用均匀权重
        if not current_weights:
            current_weights = {name: 1.0 / len(predictions) for name in predictions}
        
        # 融合方法选择
        if self.config.fusion_method == 'simple':
            return self._simple_fusion(predictions)
        elif self.config.fusion_method == 'weighted':
            return self._weighted_fusion(predictions, current_weights)
        elif self.config.fusion_method == 'adaptive':
            return self._adaptive_fusion(predictions, confidences, current_weights)
        else:
            return self._adaptive_fusion(predictions, confidences, current_weights)
    
    def _simple_fusion(self, predictions: Dict[str, Any]) -> Dict[str, Any]:
        """简单融合 - 多数投票"""
        # 收集杀号结果
        red_kills = []
        blue_kills = []
        
        for pred in predictions.values():
            if isinstance(pred, dict):
                if 'red_kills' in pred:
                    red_kills.extend(pred['red_kills'])
                if 'blue_kills' in pred:
                    blue_kills.extend(pred['blue_kills'])
        
        # 统计投票
        from collections import Counter
        red_counter = Counter(red_kills)
        blue_counter = Counter(blue_kills)
        
        # 选择最高票的号码
        final_red_kills = [num for num, count in red_counter.most_common(5)]
        final_blue_kills = [num for num, count in blue_counter.most_common(2)]
        
        return {
            'method': 'simple_fusion',
            'red_kills': final_red_kills,
            'blue_kills': final_blue_kills,
            'confidence': 0.6,
            'fusion_info': {
                'predictor_count': len(predictions),
                'red_votes': dict(red_counter),
                'blue_votes': dict(blue_counter)
            }
        }
    
    def _weighted_fusion(self, predictions: Dict[str, Any], weights: Dict[str, float]) -> Dict[str, Any]:
        """加权融合"""
        # 收集加权杀号结果
        red_scores = defaultdict(float)
        blue_scores = defaultdict(float)
        
        total_weight = 0.0
        
        for pred_name, pred in predictions.items():
            weight = weights.get(pred_name, 0.0)
            if weight <= 0:
                continue
            
            total_weight += weight
            
            if isinstance(pred, dict):
                # 红球杀号加权
                if 'red_kills' in pred:
                    for num in pred['red_kills']:
                        red_scores[num] += weight
                
                # 蓝球杀号加权
                if 'blue_kills' in pred:
                    for num in pred['blue_kills']:
                        blue_scores[num] += weight
        
        # 选择得分最高的号码
        final_red_kills = sorted(red_scores.keys(), key=lambda x: red_scores[x], reverse=True)[:5]
        final_blue_kills = sorted(blue_scores.keys(), key=lambda x: blue_scores[x], reverse=True)[:2]
        
        # 计算融合置信度
        confidence = min(0.9, 0.5 + total_weight * 0.3)
        
        return {
            'method': 'weighted_fusion',
            'red_kills': final_red_kills,
            'blue_kills': final_blue_kills,
            'confidence': confidence,
            'fusion_info': {
                'predictor_count': len(predictions),
                'total_weight': total_weight,
                'red_scores': dict(red_scores),
                'blue_scores': dict(blue_scores),
                'weights_used': weights
            }
        }
    
    def _adaptive_fusion(self, predictions: Dict[str, Any], confidences: Dict[str, float], weights: Dict[str, float]) -> Dict[str, Any]:
        """自适应融合"""
        # 结合置信度和历史权重
        adaptive_weights = {}
        
        for pred_name in predictions:
            base_weight = weights.get(pred_name, 1.0 / len(predictions))
            confidence = confidences.get(pred_name, 0.5)
            online_weight = self.online_weights.get(pred_name, base_weight)
            
            # 自适应权重计算
            adaptive_weight = (
                0.4 * base_weight +
                0.3 * confidence +
                0.3 * online_weight
            )
            
            adaptive_weights[pred_name] = adaptive_weight
        
        # 归一化权重
        total_weight = sum(adaptive_weights.values())
        if total_weight > 0:
            adaptive_weights = {k: v / total_weight for k, v in adaptive_weights.items()}
        
        # 使用自适应权重进行融合
        result = self._weighted_fusion(predictions, adaptive_weights)
        result['method'] = 'adaptive_fusion'
        result['fusion_info']['adaptive_weights'] = adaptive_weights
        
        return result
    
    def update_performance_feedback(self, prediction_result: Dict[str, Any], actual_result: Dict[str, Any]):
        """
        更新性能反馈
        
        Args:
            prediction_result: 预测结果
            actual_result: 实际结果
        """
        try:
            # 解析实际结果
            actual_red = actual_result.get('red_balls', [])
            actual_blue = actual_result.get('blue_balls', [])
            
            # 解析预测结果
            predicted_red_kills = prediction_result.get('red_kills', [])
            predicted_blue_kills = prediction_result.get('blue_kills', [])
            
            # 计算杀号成功率
            red_kill_success = all(num not in actual_red for num in predicted_red_kills)
            blue_kill_success = all(num not in actual_blue for num in predicted_blue_kills)
            overall_success = red_kill_success and blue_kill_success
            
            # 更新性能跟踪
            fusion_info = prediction_result.get('fusion_info', {})
            weights_used = fusion_info.get('weights_used', {})
            
            for predictor_name, weight in weights_used.items():
                confidence = weight  # 使用权重作为置信度代理
                self.performance_tracker.update_performance(predictor_name, overall_success, confidence)
            
            # 在线学习权重更新
            if self.config.online_learning:
                self._update_online_weights(overall_success, weights_used)
            
            # 记录融合历史
            self.fusion_history.append({
                'prediction': prediction_result,
                'actual': actual_result,
                'success': overall_success,
                'red_kill_success': red_kill_success,
                'blue_kill_success': blue_kill_success,
                'timestamp': time.time()
            })
            
            self.logger.debug(f"性能反馈更新: 杀号成功={overall_success}")
            
        except Exception as e:
            self.logger.error(f"性能反馈更新失败: {e}")
    
    def _update_online_weights(self, success: bool, weights_used: Dict[str, float]):
        """更新在线学习权重"""
        self.adaptation_count += 1
        
        # 学习率衰减
        current_lr = self.config.learning_rate * (0.99 ** (self.adaptation_count // 10))
        
        for predictor_name, weight in weights_used.items():
            if predictor_name in self.online_weights:
                # 根据成功/失败调整权重
                if success:
                    # 成功时增加权重
                    self.online_weights[predictor_name] += current_lr * weight
                else:
                    # 失败时减少权重
                    self.online_weights[predictor_name] -= current_lr * weight * 0.5
                
                # 权重约束
                self.online_weights[predictor_name] = max(
                    self.config.min_weight,
                    min(self.config.max_weight, self.online_weights[predictor_name])
                )
        
        # 权重归一化
        total_weight = sum(self.online_weights.values())
        if total_weight > 0:
            for name in self.online_weights:
                self.online_weights[name] /= total_weight
    
    def get_accuracy_metrics(self) -> Dict[str, float]:
        """获取准确率指标"""
        if not self.fusion_history:
            return {}
        
        recent_history = list(self.fusion_history)[-self.config.performance_window:]
        
        total_predictions = len(recent_history)
        successful_kills = sum(1 for h in recent_history if h['success'])
        red_kill_successes = sum(1 for h in recent_history if h['red_kill_success'])
        blue_kill_successes = sum(1 for h in recent_history if h['blue_kill_success'])
        
        return {
            'overall_kill_success_rate': successful_kills / total_predictions if total_predictions > 0 else 0.0,
            'red_kill_success_rate': red_kill_successes / total_predictions if total_predictions > 0 else 0.0,
            'blue_kill_success_rate': blue_kill_successes / total_predictions if total_predictions > 0 else 0.0,
            'total_predictions': total_predictions,
            'successful_predictions': successful_kills
        }
    
    def save_performance_data(self, filepath: str):
        """保存性能数据"""
        try:
            performance_data = {
                'config': {
                    'target_kill_success_rate': self.config.target_kill_success_rate,
                    'ensemble_size': self.config.ensemble_size,
                    'fusion_method': self.config.fusion_method
                },
                'performance_summary': self.performance_tracker.get_performance_summary(),
                'accuracy_metrics': self.get_accuracy_metrics(),
                'online_weights': dict(self.online_weights),
                'fusion_history': list(self.fusion_history)[-50:],  # 保存最近50条记录
                'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
            }
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(performance_data, f, ensure_ascii=False, indent=2, default=str)
            
            self.logger.info(f"性能数据已保存: {filepath}")
            
        except Exception as e:
            self.logger.error(f"保存性能数据失败: {e}")


class AccuracyOptimizer:
    """准确率优化器"""
    
    def __init__(self, config: AccuracyConfig = None):
        """
        初始化准确率优化器
        
        Args:
            config: 准确率提升配置
        """
        self.config = config or AccuracyConfig()
        self.logger = logging.getLogger(f"lottery_predictor.{self.__class__.__name__}")
        
        # 集成预测器
        self.ensemble_predictor = EnsemblePredictor(self.config)
        
        # 优化历史
        self.optimization_history = []
    
    def optimize_accuracy(self, data, backtest_periods: int = 30) -> Dict[str, Any]:
        """
        优化预测准确率
        
        Args:
            data: 历史数据
            backtest_periods: 回测期数
            
        Returns:
            优化结果
        """
        self.logger.info(f"开始准确率优化，回测期数: {backtest_periods}")
        
        # 初始化预测器
        self.ensemble_predictor.initialize_predictors()
        
        # 执行回测优化
        optimization_results = []
        
        start_index = len(data) - backtest_periods
        
        for i in range(start_index, len(data)):
            try:
                # 获取当前期号
                current_period = str(data.iloc[i]['期号'])
                
                # 集成预测
                prediction_result = self.ensemble_predictor.ensemble_predict(data, i)
                
                # 获取实际结果
                actual_result = self._parse_actual_result(data.iloc[i])
                
                # 更新性能反馈
                self.ensemble_predictor.update_performance_feedback(prediction_result, actual_result)
                
                # 记录优化结果
                optimization_results.append({
                    'period': current_period,
                    'prediction': prediction_result,
                    'actual': actual_result,
                    'timestamp': time.time()
                })
                
                self.logger.debug(f"期号 {current_period} 优化完成")
                
            except Exception as e:
                self.logger.error(f"期号优化失败: {e}")
                continue
        
        # 计算优化效果
        optimization_summary = self._calculate_optimization_summary(optimization_results)
        
        self.logger.info(f"准确率优化完成，杀号成功率: {optimization_summary['kill_success_rate']:.4f}")
        
        return {
            'optimization_results': optimization_results,
            'summary': optimization_summary,
            'config': self.config.__dict__
        }
    
    def _parse_actual_result(self, row) -> Dict[str, Any]:
        """解析实际开奖结果"""
        try:
            # 解析红球
            red_str = str(row.get('红球', ''))
            red_balls = [int(x.strip()) for x in red_str.split() if x.strip().isdigit()]
            
            # 解析蓝球
            blue_str = str(row.get('蓝球', ''))
            blue_balls = [int(x.strip()) for x in blue_str.split() if x.strip().isdigit()]
            
            return {
                'red_balls': red_balls,
                'blue_balls': blue_balls
            }
            
        except Exception as e:
            self.logger.error(f"解析实际结果失败: {e}")
            return {'red_balls': [], 'blue_balls': []}
    
    def _calculate_optimization_summary(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算优化总结"""
        if not results:
            return {}
        
        total_count = len(results)
        kill_success_count = 0
        red_kill_success_count = 0
        blue_kill_success_count = 0
        
        for result in results:
            prediction = result['prediction']
            actual = result['actual']
            
            # 检查杀号成功
            predicted_red_kills = prediction.get('red_kills', [])
            predicted_blue_kills = prediction.get('blue_kills', [])
            actual_red = actual.get('red_balls', [])
            actual_blue = actual.get('blue_balls', [])
            
            red_success = all(num not in actual_red for num in predicted_red_kills)
            blue_success = all(num not in actual_blue for num in predicted_blue_kills)
            
            if red_success:
                red_kill_success_count += 1
            if blue_success:
                blue_kill_success_count += 1
            if red_success and blue_success:
                kill_success_count += 1
        
        return {
            'total_predictions': total_count,
            'kill_success_count': kill_success_count,
            'kill_success_rate': kill_success_count / total_count if total_count > 0 else 0.0,
            'red_kill_success_rate': red_kill_success_count / total_count if total_count > 0 else 0.0,
            'blue_kill_success_rate': blue_kill_success_count / total_count if total_count > 0 else 0.0,
            'target_achieved': (kill_success_count / total_count) >= self.config.target_kill_success_rate if total_count > 0 else False
        }
