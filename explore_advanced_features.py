#!/usr/bin/env python3
"""
高级特征工程探索脚本
Advanced Feature Engineering Exploration Script

探索时间序列分析、频域分析、图论分析等新的特征提取方法
发现更深层的数据模式，提升预测准确率
"""

import sys
from pathlib import Path
import pandas as pd
import numpy as np
import time
import logging
import json
from typing import Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/feature_engineering.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

def load_lottery_data():
    """加载彩票数据"""
    print("📊 加载彩票数据...")
    
    data_path = Path("data/raw/dlt_data.csv")
    if data_path.exists():
        data = pd.read_csv(data_path)
        print(f"✅ 加载数据: {len(data)} 条记录")
        return data
    else:
        print("⚠️ 数据文件不存在，使用模拟数据")
        return create_mock_data()

def create_mock_data():
    """创建模拟数据用于测试"""
    print("🎲 创建模拟彩票数据...")
    
    periods = []
    for i in range(1000, 1200):  # 200期数据
        period = f"250{i:02d}"
        
        # 生成随机红球 (1-35选5)
        red_balls = sorted(np.random.choice(range(1, 36), 5, replace=False))
        red_str = ' '.join(map(str, red_balls))
        
        # 生成随机蓝球 (1-12选2)
        blue_balls = sorted(np.random.choice(range(1, 13), 2, replace=False))
        blue_str = ' '.join(map(str, blue_balls))
        
        periods.append({
            '期号': period,
            '红球': red_str,
            '蓝球': blue_str
        })
    
    return pd.DataFrame(periods)

def test_time_series_features():
    """测试时间序列特征提取"""
    print("\n📈 测试时间序列特征提取...")
    
    try:
        from src.features.advanced_feature_engineering import (
            TimeSeriesFeatureExtractor, 
            FeatureConfig
        )
        
        # 创建配置
        config = FeatureConfig(
            enable_time_series=True,
            ts_window_sizes=[5, 10, 20],
            ts_lag_features=[1, 2, 3]
        )
        
        # 创建特征提取器
        extractor = TimeSeriesFeatureExtractor(config)
        
        # 加载数据
        data = load_lottery_data()
        
        # 提取特征
        print("🚀 提取时间序列特征...")
        start_time = time.time()
        
        features = extractor.extract_features(data)
        
        end_time = time.time()
        extraction_time = end_time - start_time
        
        print(f"✅ 时间序列特征提取完成!")
        print(f"   提取时间: {extraction_time:.2f} 秒")
        print(f"   特征数量: {len(features)}")
        
        # 显示部分特征信息
        feature_names = list(features.keys())[:10]  # 显示前10个特征
        print(f"   示例特征:")
        for name in feature_names:
            feature_data = features[name]
            print(f"     {name}: 形状={feature_data.shape}, 类型={type(feature_data).__name__}")
        
        return True
        
    except Exception as e:
        print(f"❌ 时间序列特征提取测试失败: {e}")
        return False

def test_frequency_domain_features():
    """测试频域特征提取"""
    print("\n🌊 测试频域特征提取...")
    
    try:
        from src.features.advanced_feature_engineering import (
            FrequencyDomainFeatureExtractor, 
            FeatureConfig
        )
        
        # 创建配置
        config = FeatureConfig(
            enable_frequency_domain=True,
            fft_window_size=30,
            frequency_bands=5
        )
        
        # 创建特征提取器
        extractor = FrequencyDomainFeatureExtractor(config)
        
        # 加载数据
        data = load_lottery_data()
        
        # 提取特征
        print("🚀 提取频域特征...")
        start_time = time.time()
        
        features = extractor.extract_features(data)
        
        end_time = time.time()
        extraction_time = end_time - start_time
        
        print(f"✅ 频域特征提取完成!")
        print(f"   提取时间: {extraction_time:.2f} 秒")
        print(f"   特征数量: {len(features)}")
        
        if features:
            # 显示部分特征信息
            feature_names = list(features.keys())[:5]  # 显示前5个特征
            print(f"   示例特征:")
            for name in feature_names:
                feature_data = features[name]
                print(f"     {name}: 形状={feature_data.shape}")
        else:
            print("   ⚠️ 未提取到频域特征（可能缺少SciPy依赖）")
        
        return True
        
    except Exception as e:
        print(f"❌ 频域特征提取测试失败: {e}")
        return False

def test_graph_analysis_features():
    """测试图论分析特征提取"""
    print("\n🕸️ 测试图论分析特征提取...")
    
    try:
        from src.features.advanced_feature_engineering import (
            GraphAnalysisFeatureExtractor, 
            FeatureConfig
        )
        
        # 创建配置
        config = FeatureConfig(
            enable_graph_analysis=True,
            graph_window_size=20,
            graph_threshold=0.2
        )
        
        # 创建特征提取器
        extractor = GraphAnalysisFeatureExtractor(config)
        
        # 加载数据
        data = load_lottery_data()
        
        # 提取特征
        print("🚀 提取图论分析特征...")
        start_time = time.time()
        
        features = extractor.extract_features(data)
        
        end_time = time.time()
        extraction_time = end_time - start_time
        
        print(f"✅ 图论分析特征提取完成!")
        print(f"   提取时间: {extraction_time:.2f} 秒")
        print(f"   特征数量: {len(features)}")
        
        if features:
            # 显示部分特征信息
            feature_names = list(features.keys())[:5]  # 显示前5个特征
            print(f"   示例特征:")
            for name in feature_names:
                feature_data = features[name]
                print(f"     {name}: 形状={feature_data.shape}")
        else:
            print("   ⚠️ 未提取到图论特征（可能缺少NetworkX依赖）")
        
        return True
        
    except Exception as e:
        print(f"❌ 图论分析特征提取测试失败: {e}")
        return False

def test_advanced_feature_engineer():
    """测试高级特征工程器"""
    print("\n🔬 测试高级特征工程器...")
    
    try:
        from src.features.advanced_feature_engineering import (
            AdvancedFeatureEngineer, 
            FeatureConfig
        )
        
        # 创建配置
        config = FeatureConfig(
            enable_time_series=True,
            enable_frequency_domain=True,
            enable_graph_analysis=True,
            enable_statistical=True,
            enable_pattern_recognition=True,
            enable_clustering=True,
            enable_dimensionality_reduction=True,
            enable_interaction=True
        )
        
        # 创建特征工程器
        engineer = AdvancedFeatureEngineer(config)
        
        # 加载数据
        data = load_lottery_data()
        
        # 提取所有特征
        print("🚀 提取所有高级特征...")
        start_time = time.time()
        
        all_features = engineer.extract_all_features(data)
        
        end_time = time.time()
        extraction_time = end_time - start_time
        
        print(f"✅ 高级特征工程完成!")
        print(f"   提取时间: {extraction_time:.2f} 秒")
        print(f"   总特征数量: {len(all_features)}")
        
        # 分析特征类型
        feature_types = {}
        for feature_name in all_features.keys():
            if 'red' in feature_name:
                feature_type = 'red_ball'
            elif 'blue' in feature_name:
                feature_type = 'blue_ball'
            elif 'lag' in feature_name:
                feature_type = 'lag_features'
            elif 'rolling' in feature_name:
                feature_type = 'rolling_features'
            elif 'trend' in feature_name:
                feature_type = 'trend_features'
            elif 'seasonal' in feature_name:
                feature_type = 'seasonal_features'
            elif 'fft' in feature_name:
                feature_type = 'frequency_features'
            elif 'graph' in feature_name:
                feature_type = 'graph_features'
            else:
                feature_type = 'other_features'
            
            feature_types[feature_type] = feature_types.get(feature_type, 0) + 1
        
        print(f"   特征类型分布:")
        for feature_type, count in feature_types.items():
            print(f"     {feature_type}: {count} 个特征")
        
        # 保存特征信息
        results_dir = Path("feature_engineering_results")
        results_dir.mkdir(exist_ok=True)
        
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        
        # 保存特征摘要
        feature_summary = {
            'timestamp': timestamp,
            'total_features': len(all_features),
            'feature_types': feature_types,
            'extraction_time': extraction_time,
            'data_periods': len(data),
            'config': config.__dict__
        }
        
        summary_file = results_dir / f"feature_summary_{timestamp}.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(feature_summary, f, ensure_ascii=False, indent=2)
        
        print(f"📁 特征摘要已保存: {summary_file}")
        
        # 保存部分特征数据（用于分析）
        sample_features = {}
        sample_feature_names = list(all_features.keys())[:20]  # 保存前20个特征
        
        for name in sample_feature_names:
            feature_data = all_features[name]
            if hasattr(feature_data, 'tolist'):
                sample_features[name] = feature_data.tolist()
            else:
                sample_features[name] = str(feature_data)
        
        sample_file = results_dir / f"sample_features_{timestamp}.json"
        with open(sample_file, 'w', encoding='utf-8') as f:
            json.dump(sample_features, f, ensure_ascii=False, indent=2)
        
        print(f"📊 样本特征已保存: {sample_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 高级特征工程器测试失败: {e}")
        return False

def analyze_feature_importance():
    """分析特征重要性"""
    print("\n📊 分析特征重要性...")
    
    try:
        from src.features.advanced_feature_engineering import (
            AdvancedFeatureEngineer, 
            FeatureConfig
        )
        
        # 创建特征工程器
        config = FeatureConfig()
        engineer = AdvancedFeatureEngineer(config)
        
        # 加载数据
        data = load_lottery_data()
        
        # 提取特征
        print("🔍 提取特征用于重要性分析...")
        all_features = engineer.extract_all_features(data)
        
        if not all_features:
            print("⚠️ 没有提取到特征，跳过重要性分析")
            return False
        
        # 简单的特征重要性分析
        print("📈 计算特征统计信息...")
        
        feature_stats = {}
        for feature_name, feature_data in all_features.items():
            try:
                if hasattr(feature_data, '__len__') and len(feature_data) > 0:
                    # 计算基本统计量
                    if isinstance(feature_data, np.ndarray):
                        stats = {
                            'mean': float(np.mean(feature_data)),
                            'std': float(np.std(feature_data)),
                            'min': float(np.min(feature_data)),
                            'max': float(np.max(feature_data)),
                            'non_zero_ratio': float(np.count_nonzero(feature_data) / len(feature_data))
                        }
                    else:
                        stats = {
                            'mean': 0.0,
                            'std': 0.0,
                            'min': 0.0,
                            'max': 0.0,
                            'non_zero_ratio': 0.0
                        }
                    
                    feature_stats[feature_name] = stats
                    
            except Exception as e:
                print(f"   ⚠️ 特征 {feature_name} 统计计算失败: {e}")
                continue
        
        # 按方差排序（高方差特征可能更有信息量）
        sorted_features = sorted(
            feature_stats.items(), 
            key=lambda x: x[1]['std'], 
            reverse=True
        )
        
        print(f"📋 特征重要性分析结果（按标准差排序）:")
        print(f"   前10个高方差特征:")
        
        for i, (feature_name, stats) in enumerate(sorted_features[:10]):
            print(f"     {i+1:2d}. {feature_name}")
            print(f"         均值: {stats['mean']:.4f}, 标准差: {stats['std']:.4f}")
            print(f"         非零比例: {stats['non_zero_ratio']:.4f}")
        
        # 保存分析结果
        results_dir = Path("feature_engineering_results")
        results_dir.mkdir(exist_ok=True)
        
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        analysis_file = results_dir / f"feature_importance_{timestamp}.json"
        
        analysis_result = {
            'timestamp': timestamp,
            'total_features_analyzed': len(feature_stats),
            'top_features_by_variance': [
                {'name': name, 'stats': stats} 
                for name, stats in sorted_features[:20]
            ],
            'feature_statistics': feature_stats
        }
        
        with open(analysis_file, 'w', encoding='utf-8') as f:
            json.dump(analysis_result, f, ensure_ascii=False, indent=2)
        
        print(f"📁 特征重要性分析已保存: {analysis_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 特征重要性分析失败: {e}")
        return False

def generate_feature_engineering_report():
    """生成特征工程报告"""
    print("\n📋 生成特征工程报告...")
    
    try:
        # 检查依赖库可用性
        dependencies = {
            'scipy': False,
            'networkx': False,
            'sklearn': False
        }
        
        try:
            import scipy
            dependencies['scipy'] = True
        except ImportError:
            pass
        
        try:
            import networkx
            dependencies['networkx'] = True
        except ImportError:
            pass
        
        try:
            import sklearn
            dependencies['sklearn'] = True
        except ImportError:
            pass
        
        # 生成报告
        report_lines = [
            "🔬 高级特征工程探索报告",
            "=" * 60,
            f"生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}",
            "",
            "📦 依赖库状态:",
            f"   SciPy (频域分析): {'✅ 可用' if dependencies['scipy'] else '❌ 不可用'}",
            f"   NetworkX (图论分析): {'✅ 可用' if dependencies['networkx'] else '❌ 不可用'}",
            f"   Scikit-learn (机器学习): {'✅ 可用' if dependencies['sklearn'] else '❌ 不可用'}",
            "",
            "🎯 特征工程能力:",
            "   ✅ 时间序列特征提取",
            "     - 滞后特征",
            "     - 滑动窗口统计",
            "     - 趋势特征",
            "     - 季节性特征",
            "",
            f"   {'✅' if dependencies['scipy'] else '⚠️'} 频域特征提取",
            "     - FFT频谱分析",
            "     - 功率谱密度",
            "     - 频段能量分布",
            "",
            f"   {'✅' if dependencies['networkx'] else '⚠️'} 图论特征提取",
            "     - 数字共现网络",
            "     - 中心性指标",
            "     - 聚类系数",
            "     - 页面排名",
            "",
            "🚀 推荐改进方向:",
        ]
        
        # 根据依赖库状态给出建议
        if not dependencies['scipy']:
            report_lines.extend([
                "   📦 安装SciPy以启用频域分析:",
                "      pip install scipy",
                ""
            ])
        
        if not dependencies['networkx']:
            report_lines.extend([
                "   📦 安装NetworkX以启用图论分析:",
                "      pip install networkx",
                ""
            ])
        
        if not dependencies['sklearn']:
            report_lines.extend([
                "   📦 安装Scikit-learn以启用机器学习特征:",
                "      pip install scikit-learn",
                ""
            ])
        
        report_lines.extend([
            "💡 特征工程优化建议:",
            "   1. 增加更多时间窗口大小的组合",
            "   2. 探索非线性特征变换",
            "   3. 实现特征选择算法",
            "   4. 添加特征交互项",
            "   5. 考虑特征标准化和归一化",
            "",
            "🎯 预期效果:",
            "   - 提升预测模型的输入质量",
            "   - 发现隐藏的数据模式",
            "   - 增强模型的泛化能力",
            "   - 提高杀号成功率"
        ])
        
        report = "\n".join(report_lines)
        print(report)
        
        # 保存报告
        results_dir = Path("feature_engineering_results")
        results_dir.mkdir(exist_ok=True)
        
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        report_file = results_dir / f"feature_engineering_report_{timestamp}.txt"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"\n📄 特征工程报告已保存: {report_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 生成特征工程报告失败: {e}")
        return False

def main():
    """主函数"""
    print("🔬 高级特征工程探索系统")
    print("=" * 60)
    
    # 创建日志目录
    Path("logs").mkdir(exist_ok=True)
    
    tests = [
        ("时间序列特征提取", test_time_series_features),
        ("频域特征提取", test_frequency_domain_features),
        ("图论分析特征提取", test_graph_analysis_features),
        ("高级特征工程器", test_advanced_feature_engineer),
        ("特征重要性分析", analyze_feature_importance),
        ("特征工程报告生成", generate_feature_engineering_report),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append(result)
            if result:
                print(f"✅ {test_name} 成功")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"💥 {test_name} 异常: {e}")
            results.append(False)
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 高级特征工程探索结果:")
    passed = sum(results)
    total = len(results)
    print(f"✅ 通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！高级特征工程系统运行正常！")
        return 0
    elif passed >= total * 0.7:
        print("⚠️ 大部分测试通过，系统基本可用")
        return 0
    else:
        print("❌ 多个测试失败，需要修复")
        return 1

if __name__ == "__main__":
    exit(main())
