"""
号码生成模块
根据预测的状态和杀号结果生成最终的号码组合
"""

import numpy as np
from typing import List, Tuple, Set, Dict
from itertools import combinations
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.utils.utils import (
    state_to_ratio, calculate_odd_even_ratio, calculate_size_ratio_red,
    calculate_size_ratio_blue, ratio_to_state, format_numbers
)


class NumberGenerator:
    """号码生成器"""
    
    def __init__(self):
        """初始化号码生成器"""
        self.red_range = list(range(1, 36))
        self.blue_range = list(range(1, 13))
    
    def generate_numbers_by_state(self,
                                red_odd_even_state: str,
                                red_size_state: str,
                                blue_size_state: str,
                                kill_numbers: Dict[str, List[List[int]]],
                                seed: int = 0,
                                historical_data: List[Tuple[List[int], List[int]]] = None,
                                target_sum_range: Tuple[int, int] = None) -> <PERSON><PERSON>[List[int], List[int]]:
        """
        根据预测状态和杀号生成号码（增强版）

        Args:
            red_odd_even_state: 红球奇偶比状态
            red_size_state: 红球大小比状态
            blue_size_state: 蓝球大小比状态
            kill_numbers: 杀号字典
            seed: 随机种子
            historical_data: 历史开奖数据，用于避免重复模式
            target_sum_range: 目标和值范围

        Returns:
            Tuple[List[int], List[int]]: (红球号码, 蓝球号码)
        """
        # 生成红球
        red_balls = self._generate_red_balls_enhanced(
            red_odd_even_state, red_size_state, kill_numbers.get('red', []),
            seed, historical_data, target_sum_range
        )

        # 生成蓝球
        blue_balls = self._generate_blue_balls_enhanced(
            blue_size_state, kill_numbers.get('blue', []), seed, historical_data
        )

        return red_balls, blue_balls
    
    def _generate_red_balls(self,
                           odd_even_state: str,
                           size_state: str,
                           kill_lists: List[List[int]],
                           seed: int = 0) -> List[int]:
        """
        生成红球号码

        Args:
            odd_even_state: 奇偶比状态
            size_state: 大小比状态
            kill_lists: 各位置的杀号列表
            seed: 随机种子

        Returns:
            List[int]: 红球号码列表
        """
        # 解析状态
        odd_count, even_count = state_to_ratio(odd_even_state)
        small_count, big_count = state_to_ratio(size_state)

        # 创建候选号码池
        candidates = set(self.red_range)

        # 移除杀号
        for kill_list in kill_lists:
            candidates -= set(kill_list)

        candidates = list(candidates)

        # 按奇偶分类
        odd_candidates = [n for n in candidates if n % 2 == 1]
        even_candidates = [n for n in candidates if n % 2 == 0]

        # 按大小分类
        small_candidates = [n for n in candidates if 1 <= n <= 18]
        big_candidates = [n for n in candidates if 19 <= n <= 35]

        # 生成满足条件的组合
        selected_numbers = self._select_red_combination(
            odd_candidates, even_candidates, odd_count, even_count,
            small_candidates, big_candidates, small_count, big_count, seed
        )

        return sorted(selected_numbers)

    def _generate_red_balls_enhanced(self,
                                   odd_even_state: str,
                                   size_state: str,
                                   kill_lists: List[List[int]],
                                   seed: int = 0,
                                   historical_data: List[Tuple[List[int], List[int]]] = None,
                                   target_sum_range: Tuple[int, int] = None) -> List[int]:
        """
        增强版红球生成
        """
        # 解析状态
        odd_count, even_count = state_to_ratio(odd_even_state)
        small_count, big_count = state_to_ratio(size_state)

        # 创建候选号码池
        candidates = set(self.red_range)

        # 移除杀号
        for kill_list in kill_lists:
            candidates -= set(kill_list)

        candidates = list(candidates)

        # 如果有历史数据，降低最近出现号码的权重
        if historical_data:
            recent_numbers = set()
            for red_balls, _ in historical_data[:10]:  # 最近10期
                recent_numbers.update(red_balls)

            # 给最近出现的号码降权
            weighted_candidates = []
            for num in candidates:
                weight = 0.5 if num in recent_numbers else 1.0
                weighted_candidates.extend([num] * int(weight * 10))
            candidates = weighted_candidates

        # 多次尝试生成满足条件的组合
        best_combination = None
        best_score = -1

        for attempt in range(10):  # 最多尝试10次
            try:
                selected = self._select_red_combination_enhanced(
                    candidates, odd_count, even_count, small_count, big_count,
                    seed + attempt, target_sum_range
                )

                if selected and len(selected) == 5:
                    score = self._evaluate_red_combination(selected, target_sum_range)
                    if score > best_score:
                        best_score = score
                        best_combination = selected
            except:
                continue

        return sorted(best_combination) if best_combination else sorted(candidates[:5])

    def _select_red_combination_enhanced(self, candidates: List[int],
                                       odd_count: int, even_count: int,
                                       small_count: int, big_count: int,
                                       seed: int = 0,
                                       target_sum_range: Tuple[int, int] = None) -> List[int]:
        """增强版红球组合选择"""
        # 按奇偶分类
        odd_candidates = [n for n in candidates if n % 2 == 1]
        even_candidates = [n for n in candidates if n % 2 == 0]

        # 按大小分类
        small_candidates = [n for n in candidates if 1 <= n <= 18]
        big_candidates = [n for n in candidates if 19 <= n <= 35]

        # 创建四个分组
        odd_small = [n for n in odd_candidates if n in small_candidates]
        odd_big = [n for n in odd_candidates if n in big_candidates]
        even_small = [n for n in even_candidates if n in small_candidates]
        even_big = [n for n in even_candidates if n in big_candidates]

        # 尝试不同的分配策略
        for strategy in self._get_allocation_strategies(odd_count, even_count, small_count, big_count):
            odd_small_need, odd_big_need, even_small_need, even_big_need = strategy

            if (len(odd_small) >= odd_small_need and
                len(odd_big) >= odd_big_need and
                len(even_small) >= even_small_need and
                len(even_big) >= even_big_need):

                selected = []
                selected.extend(self._smart_select(odd_small, odd_small_need, seed))
                selected.extend(self._smart_select(odd_big, odd_big_need, seed + 1))
                selected.extend(self._smart_select(even_small, even_small_need, seed + 2))
                selected.extend(self._smart_select(even_big, even_big_need, seed + 3))

                # 确保没有重复号码
                selected = list(set(selected))

                if len(selected) == 5:
                    # 检查和值范围
                    if target_sum_range:
                        total_sum = sum(selected)
                        if target_sum_range[0] <= total_sum <= target_sum_range[1]:
                            return selected
                    else:
                        return selected

        # 如果无法满足条件，使用备选方案
        return self._fallback_red_selection(odd_candidates, even_candidates, odd_count, even_count, seed)

    def _smart_select(self, candidates: List[int], count: int, seed: int = 0) -> List[int]:
        """智能选择号码（确保无重复）"""
        if count <= 0 or not candidates:
            return []

        # 去重候选列表
        unique_candidates = list(set(candidates))

        if count >= len(unique_candidates):
            return unique_candidates[:]

        # 使用更复杂的评分系统
        scored_candidates = []
        for i, num in enumerate(unique_candidates):
            # 基础分数
            base_score = hash(str(num + seed)) % 1000

            # 位置分数
            position_score = (i * 17) % 100

            # 数字特性分数
            digit_score = (num * 3) % 50

            # 避免过于集中的号码
            spread_score = abs(num - 18) * 2  # 偏向中间值

            # 添加随机性但保持确定性
            random_factor = hash(str(num * seed)) % 200

            total_score = base_score + position_score + digit_score + spread_score + random_factor
            scored_candidates.append((num, total_score))

        scored_candidates.sort(key=lambda x: x[1], reverse=True)
        return [num for num, _ in scored_candidates[:count]]

    def _evaluate_red_combination(self, combination: List[int],
                                target_sum_range: Tuple[int, int] = None) -> float:
        """评估红球组合质量"""
        score = 0.0

        # 和值评分
        total_sum = sum(combination)
        if target_sum_range:
            if target_sum_range[0] <= total_sum <= target_sum_range[1]:
                score += 30
        else:
            # 默认偏好中等和值
            if 80 <= total_sum <= 120:
                score += 20

        # 跨度评分
        span = max(combination) - min(combination)
        if 15 <= span <= 25:
            score += 20

        # 分布评分（避免过于集中）
        sorted_combo = sorted(combination)
        gaps = [sorted_combo[i+1] - sorted_combo[i] for i in range(4)]
        avg_gap = sum(gaps) / len(gaps)
        if 3 <= avg_gap <= 8:
            score += 15

        # 连号评分（适量连号）
        consecutive_count = sum(1 for i in range(4) if sorted_combo[i+1] - sorted_combo[i] == 1)
        if consecutive_count <= 2:
            score += 10

        return score
    
    def _select_red_combination(self,
                               odd_candidates: List[int],
                               even_candidates: List[int],
                               odd_count: int,
                               even_count: int,
                               small_candidates: List[int],
                               big_candidates: List[int],
                               small_count: int,
                               big_count: int,
                               seed: int = 0) -> List[int]:
        """
        选择满足奇偶比和大小比的红球组合
        
        Args:
            odd_candidates: 奇数候选
            even_candidates: 偶数候选
            odd_count: 需要的奇数个数
            even_count: 需要的偶数个数
            small_candidates: 小号候选
            big_candidates: 大号候选
            small_count: 需要的小号个数
            big_count: 需要的大号个数
            
        Returns:
            List[int]: 选中的号码
        """
        # 创建四个分组：奇小、奇大、偶小、偶大
        odd_small = [n for n in odd_candidates if n in small_candidates]
        odd_big = [n for n in odd_candidates if n in big_candidates]
        even_small = [n for n in even_candidates if n in small_candidates]
        even_big = [n for n in even_candidates if n in big_candidates]
        
        # 尝试不同的分配策略
        for strategy in self._get_allocation_strategies(odd_count, even_count, small_count, big_count):
            odd_small_need, odd_big_need, even_small_need, even_big_need = strategy
            
            if (len(odd_small) >= odd_small_need and 
                len(odd_big) >= odd_big_need and
                len(even_small) >= even_small_need and 
                len(even_big) >= even_big_need):
                
                selected = []
                selected.extend(self._deterministic_select(odd_small, odd_small_need, seed))
                selected.extend(self._deterministic_select(odd_big, odd_big_need, seed + 1))
                selected.extend(self._deterministic_select(even_small, even_small_need, seed + 2))
                selected.extend(self._deterministic_select(even_big, even_big_need, seed + 3))
                
                if len(selected) == 5:
                    return selected
        
        # 如果无法满足条件，使用备选方案
        return self._fallback_red_selection(odd_candidates, even_candidates, odd_count, even_count, seed)
    
    def _get_allocation_strategies(self, odd_count: int, even_count: int, 
                                 small_count: int, big_count: int) -> List[Tuple[int, int, int, int]]:
        """
        获取分配策略
        
        Returns:
            List[Tuple[int, int, int, int]]: (奇小, 奇大, 偶小, 偶大) 的分配方案列表
        """
        strategies = []
        
        # 枚举所有可能的分配方案
        for odd_small in range(min(odd_count, small_count) + 1):
            for odd_big in range(min(odd_count - odd_small, big_count) + 1):
                if odd_small + odd_big <= odd_count:
                    remaining_odd = odd_count - odd_small - odd_big
                    if remaining_odd == 0:
                        even_small = small_count - odd_small
                        even_big = big_count - odd_big
                        
                        if (even_small >= 0 and even_big >= 0 and 
                            even_small + even_big == even_count):
                            strategies.append((odd_small, odd_big, even_small, even_big))
        
        # 按均衡性排序（优先选择分布较均匀的方案）
        strategies.sort(key=lambda x: abs(x[0] - x[1]) + abs(x[2] - x[3]))
        
        return strategies
    
    def _deterministic_select(self, candidates: List[int], count: int, seed: int = 0) -> List[int]:
        """
        确定性选择号码（基于号码值的哈希和种子）

        Args:
            candidates: 候选号码
            count: 需要选择的数量
            seed: 随机种子（用于确保不同调用产生不同结果）

        Returns:
            List[int]: 选中的号码
        """
        if count <= 0 or not candidates:
            return []

        if count >= len(candidates):
            return candidates[:]

        # 使用确定性算法选择，但加入种子变化
        # 基于号码值、位置和种子的组合哈希
        scored_candidates = []
        for i, num in enumerate(candidates):
            # 组合多个因子来创建更好的分布
            score = (hash(str(num + seed)) % 1000) + (i * 17) % 100 + (num * 3) % 50
            scored_candidates.append((num, score))

        scored_candidates.sort(key=lambda x: x[1], reverse=True)

        return [num for num, _ in scored_candidates[:count]]
    
    def _fallback_red_selection(self, odd_candidates: List[int], even_candidates: List[int],
                               odd_count: int, even_count: int, seed: int = 0) -> List[int]:
        """
        红球备选方案

        Args:
            odd_candidates: 奇数候选
            even_candidates: 偶数候选
            odd_count: 需要的奇数个数
            even_count: 需要的偶数个数
            seed: 随机种子

        Returns:
            List[int]: 选中的号码
        """
        selected = []

        # 尽量满足奇偶比
        selected.extend(self._deterministic_select(odd_candidates, min(odd_count, len(odd_candidates)), seed + 20))
        selected.extend(self._deterministic_select(even_candidates, min(even_count, len(even_candidates)), seed + 21))

        # 如果数量不足，从剩余候选中补充
        all_candidates = list(set(odd_candidates + even_candidates) - set(selected))
        remaining_count = 5 - len(selected)
        selected.extend(self._deterministic_select(all_candidates, remaining_count, seed + 22))

        return selected[:5]
    
    def _generate_blue_balls(self, size_state: str, kill_lists: List[List[int]], seed: int = 0) -> List[int]:
        """
        生成蓝球号码

        Args:
            size_state: 大小比状态
            kill_lists: 各位置的杀号列表
            seed: 随机种子

        Returns:
            List[int]: 蓝球号码列表
        """
        # 解析状态
        small_count, big_count = state_to_ratio(size_state)

        # 创建候选号码池
        candidates = set(self.blue_range)

        # 移除杀号
        for kill_list in kill_lists:
            candidates -= set(kill_list)

        candidates = list(candidates)

        # 按大小分类
        small_candidates = [n for n in candidates if 1 <= n <= 6]
        big_candidates = [n for n in candidates if 7 <= n <= 12]

        # 选择号码
        selected = []
        selected.extend(self._deterministic_select(small_candidates, min(small_count, len(small_candidates)), seed + 10))
        selected.extend(self._deterministic_select(big_candidates, min(big_count, len(big_candidates)), seed + 11))

        # 如果数量不足，补充
        if len(selected) < 2:
            remaining_candidates = list(set(candidates) - set(selected))
            remaining_count = 2 - len(selected)
            selected.extend(self._deterministic_select(remaining_candidates, remaining_count, seed + 12))

        return sorted(selected[:2])

    def _generate_blue_balls_enhanced(self, size_state: str, kill_lists: List[List[int]],
                                    seed: int = 0,
                                    historical_data: List[Tuple[List[int], List[int]]] = None) -> List[int]:
        """
        增强版蓝球生成
        """
        # 解析状态
        small_count, big_count = state_to_ratio(size_state)

        # 创建候选号码池
        candidates = set(self.blue_range)

        # 移除杀号
        for kill_list in kill_lists:
            candidates -= set(kill_list)

        candidates = list(candidates)

        # 如果有历史数据，分析最近出现的蓝球
        if historical_data:
            recent_blue_numbers = set()
            for _, blue_balls in historical_data[:5]:  # 最近5期
                recent_blue_numbers.update(blue_balls)

            # 给最近出现的号码降权
            weighted_candidates = []
            for num in candidates:
                weight = 0.3 if num in recent_blue_numbers else 1.0
                weighted_candidates.extend([num] * int(weight * 10))
            candidates = weighted_candidates

        # 按大小分类
        small_candidates = [n for n in candidates if 1 <= n <= 6]
        big_candidates = [n for n in candidates if 7 <= n <= 12]

        # 智能选择号码
        selected = []
        selected.extend(self._smart_select(small_candidates, min(small_count, len(small_candidates)), seed + 10))
        selected.extend(self._smart_select(big_candidates, min(big_count, len(big_candidates)), seed + 11))

        # 如果数量不足，补充
        if len(selected) < 2:
            remaining_candidates = list(set(candidates) - set(selected))
            remaining_count = 2 - len(selected)
            selected.extend(self._smart_select(remaining_candidates, remaining_count, seed + 12))

        return sorted(selected[:2])
    
    def validate_generated_numbers(self, red_balls: List[int], blue_balls: List[int],
                                 expected_red_odd_even: str, expected_red_size: str,
                                 expected_blue_size: str) -> Dict[str, bool]:
        """
        验证生成的号码是否符合预期状态
        
        Args:
            red_balls: 红球号码
            blue_balls: 蓝球号码
            expected_red_odd_even: 期望的红球奇偶比状态
            expected_red_size: 期望的红球大小比状态
            expected_blue_size: 期望的蓝球大小比状态
            
        Returns:
            Dict[str, bool]: 验证结果
        """
        # 验证红球奇偶比
        red_odd, red_even = calculate_odd_even_ratio(red_balls)
        actual_red_odd_even = ratio_to_state((red_odd, red_even))
        
        # 验证红球大小比
        red_big, red_small = calculate_size_ratio_red(red_balls)
        actual_red_size = ratio_to_state((red_big, red_small))

        # 验证蓝球大小比
        blue_big, blue_small = calculate_size_ratio_blue(blue_balls)
        actual_blue_size = ratio_to_state((blue_big, blue_small))
        
        return {
            'red_odd_even_match': actual_red_odd_even == expected_red_odd_even,
            'red_size_match': actual_red_size == expected_red_size,
            'blue_size_match': actual_blue_size == expected_blue_size,
            'all_match': (actual_red_odd_even == expected_red_odd_even and
                         actual_red_size == expected_red_size and
                         actual_blue_size == expected_blue_size)
        }
