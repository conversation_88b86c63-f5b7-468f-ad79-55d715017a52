#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
命中率改进实施程序
基于优化配置，具体实施系统改进

主要改进内容：
1. 修复比例预测固化问题 (红球奇偶总是3:2)
2. 降低ML预测器权重 (2.5 → 2.0)
3. 增加预测多样性和随机性
4. 实施置信度校准
5. 增强号码生成策略
"""

import os
import sys
import json
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Tuple, Any
import logging

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class HitRateImplementer:
    """命中率改进实施器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        logging.basicConfig(level=logging.INFO)
        
    def load_optimization_config(self) -> Dict:
        """加载最新的优化配置"""
        results_dir = "optimization_results"
        config_files = [f for f in os.listdir(results_dir) if f.startswith("hit_rate_optimization_config_")]
        if not config_files:
            raise FileNotFoundError("未找到优化配置文件")
            
        latest_file = max(config_files)
        file_path = os.path.join(results_dir, latest_file)
        
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def create_enhanced_ratio_predictor_module(self, config: Dict) -> str:
        """创建增强的比例预测器模块"""
        module_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的比例预测器 - 解决固化预测问题
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple
import random

class EnhancedRatioPredictor:
    """增强的比例预测器 - 多样化策略"""
    
    def __init__(self):
        self.strategies = {
            "historical_frequency": 0.3,
            "trend_analysis": 0.25,
            "cycle_detection": 0.2,
            "anti_pattern": 0.15,
            "random_diversity": 0.1
        }
        self.anti_streak_threshold = 3
        self.max_confidence = 0.8  # 降低过度自信
        self.min_confidence = 0.3
        
    def predict_red_odd_even_ratio(self, data: pd.DataFrame, current_period: int) -> Tuple[str, float]:
        """预测红球奇偶比 - 多样化策略"""
        if len(data) < 10:
            return "3:2", 0.5
            
        # 计算历史比例分布
        recent_data = data.tail(50)
        ratio_counts = {}
        
        for _, row in recent_data.iterrows():
            red_balls = [row[f'红球{i}'] for i in range(1, 6)]
            odd_count = sum(1 for x in red_balls if x % 2 == 1)
            ratio = f"{odd_count}:{5-odd_count}"
            ratio_counts[ratio] = ratio_counts.get(ratio, 0) + 1
        
        # 策略1: 历史频率 (30%)
        most_frequent = max(ratio_counts, key=ratio_counts.get) if ratio_counts else "3:2"
        
        # 策略2: 趋势分析 (25%)
        recent_5 = data.tail(5)
        trend_ratios = []
        for _, row in recent_5.iterrows():
            red_balls = [row[f'红球{i}'] for i in range(1, 6)]
            odd_count = sum(1 for x in red_balls if x % 2 == 1)
            trend_ratios.append(odd_count)
        
        trend_avg = np.mean(trend_ratios) if trend_ratios else 2.5
        trend_prediction = f"{int(round(trend_avg))}:{5-int(round(trend_avg))}"
        
        # 策略3: 反模式 (15%) - 避免连续相同预测
        last_3_ratios = []
        for _, row in data.tail(3).iterrows():
            red_balls = [row[f'红球{i}'] for i in range(1, 6)]
            odd_count = sum(1 for x in red_balls if x % 2 == 1)
            last_3_ratios.append(f"{odd_count}:{5-odd_count}")
        
        # 如果最近3期都是同一比例，倾向于选择不同的
        if len(set(last_3_ratios)) == 1 and len(last_3_ratios) >= 3:
            all_ratios = ["0:5", "1:4", "2:3", "3:2", "4:1", "5:0"]
            anti_pattern_prediction = random.choice([r for r in all_ratios if r != last_3_ratios[0]])
        else:
            anti_pattern_prediction = most_frequent
        
        # 策略4: 随机多样性 (10%)
        all_ratios = ["1:4", "2:3", "3:2", "4:1"]  # 常见比例
        random_prediction = random.choice(all_ratios)
        
        # 综合决策
        predictions = [most_frequent, trend_prediction, anti_pattern_prediction, random_prediction]
        weights = [0.3, 0.25, 0.15, 0.1]
        
        # 加权随机选择
        final_prediction = np.random.choice(predictions, p=weights/np.sum(weights))
        
        # 计算置信度 (降低过度自信)
        consistency = predictions.count(final_prediction) / len(predictions)
        confidence = self.min_confidence + (self.max_confidence - self.min_confidence) * consistency
        confidence = min(confidence, self.max_confidence)
        
        return final_prediction, confidence
    
    def predict_red_size_ratio(self, data: pd.DataFrame, current_period: int) -> Tuple[str, float]:
        """预测红球大小比 - 类似的多样化策略"""
        if len(data) < 10:
            return "2:3", 0.5
            
        # 使用类似的多样化策略
        recent_data = data.tail(50)
        ratio_counts = {}
        
        for _, row in recent_data.iterrows():
            red_balls = [row[f'红球{i}'] for i in range(1, 6)]
            big_count = sum(1 for x in red_balls if x > 18)
            ratio = f"{big_count}:{5-big_count}"
            ratio_counts[ratio] = ratio_counts.get(ratio, 0) + 1
        
        # 应用多样化策略
        most_frequent = max(ratio_counts, key=ratio_counts.get) if ratio_counts else "2:3"
        
        # 添加随机性避免固化
        all_ratios = ["1:4", "2:3", "3:2", "4:1"]
        if random.random() < 0.2:  # 20%概率选择随机比例
            final_prediction = random.choice(all_ratios)
            confidence = 0.4
        else:
            final_prediction = most_frequent
            confidence = min(0.7, self.max_confidence)
        
        return final_prediction, confidence
    
    def predict_blue_size_ratio(self, data: pd.DataFrame, current_period: int) -> Tuple[str, float]:
        """预测蓝球大小比 - 多样化策略"""
        if len(data) < 10:
            return "1:1", 0.5
            
        recent_data = data.tail(30)
        ratio_counts = {}
        
        for _, row in recent_data.iterrows():
            blue_balls = [row[f'蓝球{i}'] for i in range(1, 3)]
            big_count = sum(1 for x in blue_balls if x > 6)
            ratio = f"{big_count}:{2-big_count}"
            ratio_counts[ratio] = ratio_counts.get(ratio, 0) + 1
        
        most_frequent = max(ratio_counts, key=ratio_counts.get) if ratio_counts else "1:1"
        
        # 蓝球预测添加更多随机性
        all_ratios = ["0:2", "1:1", "2:0"]
        if random.random() < 0.3:  # 30%概率随机选择
            final_prediction = random.choice(all_ratios)
            confidence = 0.4
        else:
            final_prediction = most_frequent
            confidence = min(0.6, self.max_confidence)
        
        return final_prediction, confidence
'''
        return module_code
    
    def create_weight_adjustment_script(self, config: Dict) -> str:
        """创建权重调整脚本"""
        weights = config.get("optimized_weights", {})
        
        script = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
权重调整脚本 - 基于回测结果优化
"""

# 优化后的权重配置
OPTIMIZED_WEIGHTS = {json.dumps(weights, indent=4, ensure_ascii=False)}

def apply_weight_adjustments():
    """应用权重调整"""
    print("🔧 应用权重优化:")
    for alg, wt in OPTIMIZED_WEIGHTS.items():
        print(f"  {{alg}}: {{wt}}")

    # 这里可以添加具体的权重应用逻辑
    return OPTIMIZED_WEIGHTS

if __name__ == "__main__":
    apply_weight_adjustments()
'''
        return script
    
    def create_enhanced_number_generator(self, config: Dict) -> str:
        """创建增强的号码生成器"""
        generator_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的号码生成器 - 提升2+1命中率
"""

import numpy as np
import pandas as pd
from typing import List, Tuple
import random

class EnhancedNumberGenerator:
    """增强的号码生成器"""
    
    def __init__(self):
        self.strategies = {
            "hot_cold_balance": 0.3,
            "position_analysis": 0.25,
            "sum_control": 0.2,
            "span_control": 0.15,
            "ac_value_control": 0.1
        }
    
    def generate_red_balls(self, data: pd.DataFrame, kill_numbers: List[int], 
                          target_ratio: str) -> List[int]:
        """生成红球号码 - 多策略融合"""
        available_numbers = [i for i in range(1, 36) if i not in kill_numbers]
        
        if len(available_numbers) < 5:
            available_numbers = list(range(1, 36))
        
        # 策略1: 热冷平衡 (30%)
        hot_numbers = self.get_hot_numbers(data, available_numbers, count=15)
        cold_numbers = self.get_cold_numbers(data, available_numbers, count=10)
        
        # 策略2: 位置分析 (25%)
        position_preferred = self.get_position_preferred_numbers(data, available_numbers)
        
        # 策略3: 和值控制 (20%)
        sum_controlled = self.filter_by_sum_range(available_numbers, target_sum=(85, 115))
        
        # 策略4: 跨度控制 (15%)
        span_controlled = self.filter_by_span_range(available_numbers, target_span=(18, 28))
        
        # 综合选择
        candidate_pools = [hot_numbers, position_preferred, sum_controlled, span_controlled]
        weights = [0.3, 0.25, 0.2, 0.15]
        
        selected_numbers = []
        for i in range(5):
            # 加权随机选择候选池
            pool_idx = np.random.choice(len(candidate_pools), p=weights)
            pool = candidate_pools[pool_idx]
            
            # 从选中的池中随机选择
            if pool and len([n for n in pool if n not in selected_numbers]) > 0:
                available_in_pool = [n for n in pool if n not in selected_numbers]
                selected_numbers.append(random.choice(available_in_pool))
            else:
                # 备选方案
                remaining = [n for n in available_numbers if n not in selected_numbers]
                if remaining:
                    selected_numbers.append(random.choice(remaining))
        
        # 确保满足目标比例
        selected_numbers = self.adjust_for_ratio(selected_numbers, target_ratio, available_numbers)
        
        return sorted(selected_numbers[:5])
    
    def get_hot_numbers(self, data: pd.DataFrame, available: List[int], count: int) -> List[int]:
        """获取热号"""
        recent_data = data.tail(20)
        number_counts = {}
        
        for _, row in recent_data.iterrows():
            for i in range(1, 6):
                num = row[f'红球{i}']
                if num in available:
                    number_counts[num] = number_counts.get(num, 0) + 1
        
        sorted_numbers = sorted(number_counts.items(), key=lambda x: x[1], reverse=True)
        return [num for num, _ in sorted_numbers[:count]]
    
    def get_cold_numbers(self, data: pd.DataFrame, available: List[int], count: int) -> List[int]:
        """获取冷号"""
        recent_data = data.tail(30)
        appeared_numbers = set()
        
        for _, row in recent_data.iterrows():
            for i in range(1, 6):
                appeared_numbers.add(row[f'红球{i}'])
        
        cold_numbers = [num for num in available if num not in appeared_numbers]
        return cold_numbers[:count] if cold_numbers else available[:count]
    
    def get_position_preferred_numbers(self, data: pd.DataFrame, available: List[int]) -> List[int]:
        """基于位置分析的首选号码"""
        position_weights = [1.0, 1.1, 1.2, 1.1, 1.0]  # 中间位置权重更高
        recent_data = data.tail(15)
        
        position_scores = {}
        for num in available:
            score = 0
            for _, row in recent_data.iterrows():
                for pos in range(5):
                    if row[f'红球{pos+1}'] == num:
                        score += position_weights[pos]
            position_scores[num] = score
        
        sorted_numbers = sorted(position_scores.items(), key=lambda x: x[1], reverse=True)
        return [num for num, _ in sorted_numbers[:15]]
    
    def filter_by_sum_range(self, numbers: List[int], target_sum: Tuple[int, int]) -> List[int]:
        """根据和值范围过滤号码"""
        # 简化实现：返回中等大小的号码
        return [num for num in numbers if 8 <= num <= 28]
    
    def filter_by_span_range(self, numbers: List[int], target_span: Tuple[int, int]) -> List[int]:
        """根据跨度范围过滤号码"""
        # 简化实现：避免极端号码
        return [num for num in numbers if 5 <= num <= 30]
    
    def adjust_for_ratio(self, numbers: List[int], target_ratio: str, available: List[int]) -> List[int]:
        """调整号码以满足目标比例"""
        if not target_ratio or ":" not in target_ratio:
            return numbers
        
        try:
            odd_target, even_target = map(int, target_ratio.split(":"))
            current_odd = sum(1 for n in numbers if n % 2 == 1)
            current_even = len(numbers) - current_odd
            
            # 如果比例不匹配，进行调整
            if current_odd != odd_target:
                # 简化调整逻辑
                adjusted = numbers.copy()
                if current_odd < odd_target:
                    # 需要更多奇数
                    for i, num in enumerate(adjusted):
                        if num % 2 == 0:  # 偶数
                            odd_candidates = [n for n in available if n % 2 == 1 and n not in adjusted]
                            if odd_candidates:
                                adjusted[i] = random.choice(odd_candidates)
                                break
                return adjusted
        except:
            pass
        
        return numbers
    
    def generate_blue_balls(self, data: pd.DataFrame, kill_numbers: List[int], 
                           target_ratio: str) -> List[int]:
        """生成蓝球号码"""
        available_numbers = [i for i in range(1, 13) if i not in kill_numbers]
        
        if len(available_numbers) < 2:
            available_numbers = list(range(1, 13))
        
        # 简单的热冷平衡策略
        recent_data = data.tail(15)
        blue_counts = {}
        
        for _, row in recent_data.iterrows():
            for i in range(1, 3):
                num = row[f'蓝球{i}']
                if num in available_numbers:
                    blue_counts[num] = blue_counts.get(num, 0) + 1
        
        # 选择适中频率的号码
        if blue_counts:
            sorted_blues = sorted(blue_counts.items(), key=lambda x: x[1])
            mid_range = sorted_blues[len(sorted_blues)//4:3*len(sorted_blues)//4]
            candidates = [num for num, _ in mid_range] if mid_range else available_numbers
        else:
            candidates = available_numbers
        
        # 随机选择2个
        selected = random.sample(candidates, min(2, len(candidates)))
        return sorted(selected)
'''
        return generator_code
    
    def implement_improvements(self) -> Dict:
        """实施所有改进"""
        print("🚀 开始实施命中率改进")
        print("=" * 60)
        
        # 1. 加载优化配置
        print("📊 第一步: 加载优化配置")
        config = self.load_optimization_config()
        print("✅ 配置加载完成")
        
        # 2. 创建增强模块
        print("\n🔧 第二步: 创建增强模块")
        
        # 创建增强比例预测器
        ratio_predictor_code = self.create_enhanced_ratio_predictor_module(config)
        with open("enhanced_ratio_predictor.py", "w", encoding="utf-8") as f:
            f.write(ratio_predictor_code)
        print("✅ 增强比例预测器已创建")
        
        # 创建权重调整脚本
        weight_script = self.create_weight_adjustment_script(config)
        with open("apply_weight_optimization.py", "w", encoding="utf-8") as f:
            f.write(weight_script)
        print("✅ 权重调整脚本已创建")
        
        # 创建增强号码生成器
        generator_code = self.create_enhanced_number_generator(config)
        with open("enhanced_number_generator.py", "w", encoding="utf-8") as f:
            f.write(generator_code)
        print("✅ 增强号码生成器已创建")
        
        # 3. 生成实施报告
        print("\n📋 第三步: 生成实施报告")
        implementation_report = {
            "implementation_date": datetime.now().isoformat(),
            "created_files": [
                "enhanced_ratio_predictor.py",
                "apply_weight_optimization.py", 
                "enhanced_number_generator.py"
            ],
            "key_improvements": [
                "修复比例预测固化问题 (红球奇偶不再总是3:2)",
                "降低ML预测器权重 (2.5 → 2.0)",
                "增加预测多样性和随机性 (20-30%随机因子)",
                "实施置信度校准 (最大0.8，避免过度自信)",
                "增强号码生成策略 (热冷平衡、位置分析等)"
            ],
            "expected_improvements": config.get("target_improvements", {}),
            "next_steps": [
                "集成新模块到主系统",
                "运行回测验证改进效果",
                "根据结果进一步调优参数"
            ]
        }
        
        # 保存实施报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"optimization_results/implementation_report_{timestamp}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(implementation_report, f, ensure_ascii=False, indent=2)
        
        print(f"📁 实施报告已保存: {report_file}")
        
        print("\n🎯 实施完成! 主要成果:")
        print("1. ✅ 创建了多样化比例预测器")
        print("2. ✅ 生成了权重优化配置")
        print("3. ✅ 开发了增强号码生成器")
        print("4. ✅ 实施了置信度校准机制")
        print("5. ✅ 增加了预测随机性和多样性")
        
        print("\n📈 预期改进效果:")
        for key, value in config.get("target_improvements", {}).items():
            print(f"  {key}: {value}")
        
        return implementation_report

if __name__ == "__main__":
    implementer = HitRateImplementer()
    results = implementer.implement_improvements()
