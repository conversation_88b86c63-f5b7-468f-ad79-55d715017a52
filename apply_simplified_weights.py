#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
应用简化权重配置
自动生成的权重配置应用脚本
"""

# 简化权重配置 (基于准确率分析优化)
SIMPLIFIED_WEIGHTS = {
    "enhanced_kill": 2.0,
    "traditional_markov_red_odd_even": 1.5,
    "traditional_markov_red_size": 1.5,
    "traditional_markov_blue_size": 1.3,
    "traditional_bayes_red_odd_even": 1.2,
    "traditional_bayes_red_size": 1.2,
    "traditional_bayes_blue_size": 1.0,
    "enhanced_historical_frequency": 1.6,
    "enhanced_transition_pattern": 1.4,
    "enhanced_correlation": 1.2,
    "ml_ratio_predictor": 1.0,
    "specialized_red_odd_even_optimizer": 0.8
}

def apply_weights_to_main_system():
    """将简化权重应用到主系统"""
    import sys
    sys.path.append('src')
    
    try:
        from src.systems.main import LotteryPredictor

        # 创建系统实例
        system = LotteryPredictor()
        
        # 应用简化权重
        for algorithm_name, weight in SIMPLIFIED_WEIGHTS.items():
            try:
                if algorithm_name in system.ensemble_manager.voting_weights:
                    # 直接更新权重
                    system.ensemble_manager.voting_weights[algorithm_name] = weight
                    if algorithm_name in system.ensemble_manager.performance_tracker:
                        system.ensemble_manager.performance_tracker[algorithm_name].weight = weight
                    print(f"✅ 更新权重: {algorithm_name} = {weight}")
                else:
                    print(f"⚠️ 算法未注册: {algorithm_name}")
            except Exception as e:
                print(f"❌ 权重更新失败 {algorithm_name}: {e}")
        
        print("🎯 权重配置应用完成")
        return True
        
    except Exception as e:
        print(f"❌ 权重应用失败: {e}")
        return False

if __name__ == "__main__":
    apply_weights_to_main_system()
