"""
大乐透预测系统 - 终极升级版主程序
在原有main.py基础上集成所有优化功能
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple
import os
import argparse
from datetime import datetime

# 导入原有组件
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.systems.main import LotteryPredictor
from src.utils.utils import load_data, parse_numbers, check_hit_2_plus_1, format_numbers

# 导入新的优化组件
try:
    from ultimate_prediction_system import UltimatePredictionSystem

    ULTIMATE_AVAILABLE = True
except ImportError:
    print("⚠️ 终极预测系统组件未完全加载，将使用基础功能")
    ULTIMATE_AVAILABLE = False

try:
    from super_prediction_system import SuperPredictionSystem

    SUPER_AVAILABLE = True
except ImportError:
    print("⚠️ 超级预测系统组件未完全加载")
    SUPER_AVAILABLE = False

try:
    from enhanced_neural_predictor import EnhancedNeuralPredictor
    from external_features import ExternalFeatureExtractor
    from balanced_dynamic_generator import BalancedDynamicGenerator

    ENHANCED_AVAILABLE = True
except ImportError:
    print("⚠️ 增强组件未完全加载")
    ENHANCED_AVAILABLE = False


class UltimateLotteryPredictor:
    """终极大乐透预测系统"""

    def __init__(self, mode: str = "auto"):
        """
        初始化终极预测系统

        Args:
            mode: 运行模式 ("basic", "enhanced", "super", "ultimate", "auto")
        """
        print("🚀 初始化终极大乐透预测系统...")

        self.mode = mode
        self.data = load_data()

        # 基础预测器（始终可用）
        self.basic_predictor = LotteryPredictor()

        # 根据可用性和模式选择预测器
        self.predictors = {"basic": self.basic_predictor}

        # 增强组件
        if ENHANCED_AVAILABLE and mode in ["enhanced", "super", "ultimate", "auto"]:
            try:
                self.enhanced_neural = EnhancedNeuralPredictor()
                self.external_features = ExternalFeatureExtractor()
                self.balanced_generator = BalancedDynamicGenerator()
                self.predictors["enhanced"] = "available"
                print("✅ 增强组件加载成功")
            except Exception as e:
                print(f"⚠️ 增强组件加载失败: {e}")

        # 超级预测系统
        if SUPER_AVAILABLE and mode in ["super", "ultimate", "auto"]:
            try:
                self.super_system = SuperPredictionSystem()
                self.predictors["super"] = self.super_system
                print("✅ 超级预测系统加载成功")
            except Exception as e:
                print(f"⚠️ 超级预测系统加载失败: {e}")

        # 终极预测系统
        if ULTIMATE_AVAILABLE and mode in ["ultimate", "auto"]:
            try:
                self.ultimate_system = UltimatePredictionSystem()
                self.predictors["ultimate"] = self.ultimate_system
                print("✅ 终极预测系统加载成功")
            except Exception as e:
                print(f"⚠️ 终极预测系统加载失败: {e}")

        # 自动选择最佳可用模式
        if mode == "auto":
            if "ultimate" in self.predictors:
                self.active_mode = "ultimate"
            elif "super" in self.predictors:
                self.active_mode = "super"
            elif "enhanced" in self.predictors:
                self.active_mode = "enhanced"
            else:
                self.active_mode = "basic"
        else:
            self.active_mode = mode if mode in self.predictors else "basic"

        print(f"🎯 当前运行模式: {self.active_mode.upper()}")

        # 性能统计
        self.performance_stats = {
            "basic": {"predictions": 0, "hits": 0},
            "enhanced": {"predictions": 0, "hits": 0},
            "super": {"predictions": 0, "hits": 0},
            "ultimate": {"predictions": 0, "hits": 0},
        }

    def predict_next_period(self, current_period_index: int = 0) -> Dict:
        """
        预测下一期号码（使用当前最佳模式）

        Args:
            current_period_index: 当前期次索引

        Returns:
            Dict: 预测结果
        """
        print(f"\n🎯 使用 {self.active_mode.upper()} 模式预测...")

        try:
            if self.active_mode == "ultimate" and "ultimate" in self.predictors:
                # 初始化终极系统（如果尚未初始化）
                if not self.ultimate_system.is_initialized:
                    print("初始化终极预测系统...")
                    if not self.ultimate_system.initialize_ultimate_system(
                        full_optimization=False
                    ):
                        print("终极系统初始化失败，降级到超级模式")
                        return self._fallback_predict(current_period_index, "super")

                prediction = self.ultimate_system.predict_ultimate(current_period_index)
                prediction["prediction_mode"] = "ultimate"
                return prediction

            elif self.active_mode == "super" and "super" in self.predictors:
                # 初始化超级系统（如果尚未初始化）
                if not self.super_system.is_initialized:
                    print("初始化超级预测系统...")
                    if not self.super_system.initialize_system(retrain_neural=False):
                        print("超级系统初始化失败，降级到增强模式")
                        return self._fallback_predict(current_period_index, "enhanced")

                prediction = self.super_system.predict_next_period_super(
                    current_period_index
                )
                prediction["prediction_mode"] = "super"
                return prediction

            elif self.active_mode == "enhanced" and "enhanced" in self.predictors:
                # 使用增强组件进行预测
                prediction = self._enhanced_predict(current_period_index)
                prediction["prediction_mode"] = "enhanced"
                return prediction

            else:
                # 使用基础预测器
                prediction = self.basic_predictor.predict_next_period(
                    current_period_index
                )
                prediction["prediction_mode"] = "basic"
                return prediction

        except Exception as e:
            print(f"❌ {self.active_mode} 模式预测失败: {e}")
            return self._fallback_predict(current_period_index, "basic")

    def _enhanced_predict(self, current_period_index: int) -> Dict:
        """增强模式预测"""
        # 获取基础预测
        base_prediction = self.basic_predictor.predict_next_period(current_period_index)

        # 如果增强神经网络已训练，添加神经网络预测
        if hasattr(self, "enhanced_neural") and self.enhanced_neural.is_trained:
            try:
                recent_data = self.data.iloc[current_period_index:]
                neural_predictions = self.enhanced_neural.predict_ensemble(
                    recent_data, lookback=8
                )

                # 集成预测结果
                for key, (pred_value, confidence) in neural_predictions.items():
                    if key in base_prediction["predictions"]:
                        base_conf = base_prediction["predictions"][key][1]
                        if confidence > base_conf:
                            base_prediction["predictions"][key] = (
                                pred_value,
                                confidence,
                            )

                base_prediction["neural_predictions"] = neural_predictions
            except Exception as e:
                print(f"神经网络预测失败: {e}")

        # 添加外部特征分析
        if hasattr(self, "external_features"):
            try:
                current_period = self.data.iloc[current_period_index]["期号"]
                external_features = (
                    self.external_features.extract_all_external_features(current_period)
                )
                base_prediction["external_features"] = external_features
            except Exception as e:
                print(f"外部特征提取失败: {e}")

        return base_prediction

    def _fallback_predict(self, current_period_index: int, fallback_mode: str) -> Dict:
        """降级预测"""
        print(f"降级到 {fallback_mode} 模式...")

        if fallback_mode == "super" and "super" in self.predictors:
            return self._enhanced_predict(current_period_index)
        elif fallback_mode == "enhanced" and "enhanced" in self.predictors:
            return self._enhanced_predict(current_period_index)
        else:
            prediction = self.basic_predictor.predict_next_period(current_period_index)
            prediction["prediction_mode"] = "basic"
            return prediction

    def run_comprehensive_backtest(
        self, num_periods: int = 30, display_periods: int = 5
    ) -> Dict:
        """
        运行综合回测（比较所有可用模式）- 使用统一框架

        Args:
            num_periods: 回测期数
            display_periods: 显示的期数

        Returns:
            Dict: 回测结果
        """
        print("🧪 开始综合回测 - 使用统一框架...")
        print("=" * 60)

        try:
            # 导入统一框架
            from ..framework import BacktestFramework, BacktestConfig, ResultDisplayer
            from ..framework.predictor_adapter import create_predictor_adapter

            results = {}

            # 测试所有可用的预测模式
            available_modes = [
                mode
                for mode in ["basic", "enhanced", "super", "ultimate"]
                if mode in self.predictors or mode == "basic"
            ]

            for mode in available_modes:
                print(f"\n📊 测试 {mode.upper()} 模式...")

                # 临时切换到当前测试模式
                original_mode = self.active_mode
                self.active_mode = mode

                try:
                    # 创建适配器
                    adapter = create_predictor_adapter("lottery", self)

                    # 创建框架
                    framework = BacktestFramework(self.data)

                    # 配置回测
                    config = BacktestConfig(
                        num_periods=num_periods,
                        min_train_periods=20,
                        display_periods=display_periods,
                        enable_detailed_output=False,  # 简化输出
                        enable_statistics=True,
                    )

                    # 运行回测
                    result = framework.run_backtest(adapter, config)
                    results[mode] = result

                    # 显示简要统计
                    print(f"  {mode} 模式结果:")
                    print(f"    2+1命中率: {result.statistics.hit_2_plus_1_rate:.1%}")
                    print(
                        f"    状态预测综合命中率: {(result.statistics.red_odd_even_rate + result.statistics.red_size_rate + result.statistics.blue_size_rate) / 3:.1%}"
                    )

                except Exception as e:
                    print(f"  {mode} 模式测试失败: {e}")
                    results[mode] = {"error": str(e)}

                # 恢复原始模式
                self.active_mode = original_mode

            # 显示对比结果
            self._print_unified_comparison_results(results, display_periods)

            return results

        except ImportError as e:
            print(f"⚠️ 统一框架导入失败，使用原始回测方法: {e}")
            return self._run_comprehensive_backtest_legacy(num_periods, display_periods)
        except Exception as e:
            print(f"⚠️ 统一框架回测失败，使用原始回测方法: {e}")
            return self._run_comprehensive_backtest_legacy(num_periods, display_periods)

    def _run_comprehensive_backtest_legacy(
        self, num_periods: int = 30, display_periods: int = 5
    ) -> Dict:
        """原始综合回测方法（备份）"""
        print("🧪 开始综合回测（原始方法）...")
        print("=" * 60)

        results = {}

        # 测试所有可用的预测模式
        available_modes = [
            mode
            for mode in ["basic", "enhanced", "super", "ultimate"]
            if mode in self.predictors or mode == "basic"
        ]

        for mode in available_modes:
            print(f"\n📊 测试 {mode.upper()} 模式...")

            # 临时切换到当前测试模式
            original_mode = self.active_mode
            self.active_mode = mode

            try:
                mode_results = self._run_single_mode_backtest(num_periods, mode)
                results[mode] = mode_results

                # 显示简要统计
                stats = mode_results["statistics"]
                print(f"  {mode} 模式结果:")
                print(f"    2+1命中率: {stats.get('hit_2_plus_1_rate', 0):.1%}")
                print(
                    f"    状态预测综合命中率: {stats.get('overall_state_rate', 0):.1%}"
                )

            except Exception as e:
                print(f"  {mode} 模式测试失败: {e}")
                results[mode] = {"error": str(e)}

            # 恢复原始模式
            self.active_mode = original_mode

        # 显示对比结果
        self._print_comparison_results(results, display_periods)

        return results

    def _print_unified_comparison_results(self, results: Dict, display_periods: int):
        """打印统一框架的对比结果"""
        print("\n" + "=" * 60)
        print("📊 综合回测对比结果（统一框架）")
        print("=" * 60)

        # 创建对比表格
        modes = [mode for mode in results.keys() if "error" not in results[mode]]

        if not modes:
            print("❌ 没有成功的测试结果")
            return

        print(
            f"{'模式':<12} {'2+1命中率':<12} {'状态命中率':<12} {'杀号成功率':<12} {'总成功率':<10}"
        )
        print("-" * 70)

        best_2_plus_1 = 0
        best_mode = "basic"

        for mode in modes:
            if isinstance(results[mode], dict) and "error" in results[mode]:
                continue

            result = results[mode]
            stats = result.statistics
            hit_2_plus_1_rate = stats.hit_2_plus_1_rate
            state_rate = (
                stats.red_odd_even_rate + stats.red_size_rate + stats.blue_size_rate
            ) / 3
            kill_rate = (stats.red_kill_success_rate + stats.blue_kill_success_rate) / 2
            overall_rate = result.get_success_rate()

            print(
                f"{mode.upper():<12} {hit_2_plus_1_rate:<12.1%} {state_rate:<12.1%} {kill_rate:<12.1%} {overall_rate:<10.1%}"
            )

            if hit_2_plus_1_rate > best_2_plus_1:
                best_2_plus_1 = hit_2_plus_1_rate
                best_mode = mode

        print("\n🏆 最佳模式推荐:")
        print(f"   {best_mode.upper()} 模式 (2+1命中率: {best_2_plus_1:.1%})")

        # 显示详细结果示例
        if best_mode in results and display_periods > 0:
            print(f"\n📋 {best_mode.upper()} 模式详细结果示例:")
            best_result = results[best_mode]
            display_results = best_result.get_display_results()[:display_periods]

            for period_result in display_results:
                period = period_result.period_number
                prediction = period_result.prediction
                actual = period_result.actual_result
                evaluation = period_result.evaluation

                print(f"\n期号 {period}:")
                if prediction.generated_numbers:
                    pred_red, pred_blue = prediction.generated_numbers
                    actual_red, actual_blue = actual.red_balls, actual.blue_balls
                    print(
                        f"  预测: {format_numbers(pred_red)}——{format_numbers(pred_blue)}"
                    )
                    print(
                        f"  实际: {format_numbers(actual_red)}——{format_numbers(actual_blue)}"
                    )
                    print(
                        f"  命中: 红球{evaluation.red_hits}个, 蓝球{evaluation.blue_hits}个"
                    )
                    if evaluation.hit_2_plus_1:
                        print("  🎯 2+1命中!")

    def _run_single_mode_backtest(self, num_periods: int, mode: str) -> Dict:
        """运行单一模式的回测"""
        backtest_results = []
        hit_2_plus_1_results = []

        max_backtest = min(num_periods, len(self.data) - 20)

        for i in range(max_backtest):
            try:
                # 预测
                prediction = self.predict_next_period(i)

                # 获取实际结果
                actual_row = self.data.iloc[i]
                actual_red, actual_blue = parse_numbers(actual_row)

                # 评估预测
                evaluation = self._evaluate_prediction(
                    prediction, actual_red, actual_blue
                )

                backtest_results.append(
                    {
                        "period": actual_row["期号"],
                        "prediction": prediction,
                        "actual": (actual_red, actual_blue),
                        "evaluation": evaluation,
                    }
                )

                # 检查2+1命中
                if "generated_numbers" in prediction:
                    hit_2_plus_1 = check_hit_2_plus_1(
                        prediction["generated_numbers"], (actual_red, actual_blue)
                    )
                    hit_2_plus_1_results.append(hit_2_plus_1)

            except Exception as e:
                print(f"  期次 {i+1} 预测失败: {e}")
                continue

        # 计算统计
        statistics = self._calculate_statistics(backtest_results, hit_2_plus_1_results)

        return {"mode": mode, "results": backtest_results, "statistics": statistics}

    def _evaluate_prediction(
        self, prediction: Dict, actual_red: List[int], actual_blue: List[int]
    ) -> Dict:
        """评估单次预测"""
        evaluation = {}

        # 状态预测评估
        if "predictions" in prediction:
            preds = prediction["predictions"]

            # 计算实际状态
            actual_odd = sum(1 for x in actual_red if x % 2 == 1)
            actual_red_odd_even = f"{actual_odd}:{5-actual_odd}"

            actual_small = sum(1 for x in actual_red if x <= 18)
            actual_red_size = f"{actual_small}:{5-actual_small}"

            actual_blue_small = sum(1 for x in actual_blue if x <= 6)
            actual_blue_size = f"{actual_blue_small}:{2-actual_blue_small}"

            # 检查命中
            evaluation["red_odd_even_hit"] = (
                preds.get("red_odd_even", ("", 0))[0] == actual_red_odd_even
            )
            evaluation["red_size_hit"] = (
                preds.get("red_size", ("", 0))[0] == actual_red_size
            )
            evaluation["blue_size_hit"] = (
                preds.get("blue_size", ("", 0))[0] == actual_blue_size
            )

        # 号码预测评估
        if "generated_numbers" in prediction:
            pred_red, pred_blue = prediction["generated_numbers"]
            red_hits = len(set(pred_red) & set(actual_red))
            blue_hits = len(set(pred_blue) & set(actual_blue))

            evaluation["red_hits"] = red_hits
            evaluation["blue_hits"] = blue_hits
            evaluation["hit_2_plus_1"] = check_hit_2_plus_1(
                (pred_red, pred_blue), (actual_red, actual_blue)
            )

        return evaluation

    def _calculate_statistics(
        self, results: List[Dict], hit_2_plus_1_results: List[bool]
    ) -> Dict:
        """计算统计信息"""
        if not results:
            return {}

        total = len(results)

        # 状态预测统计
        red_odd_even_hits = sum(
            1 for r in results if r["evaluation"].get("red_odd_even_hit", False)
        )
        red_size_hits = sum(
            1 for r in results if r["evaluation"].get("red_size_hit", False)
        )
        blue_size_hits = sum(
            1 for r in results if r["evaluation"].get("blue_size_hit", False)
        )

        # 号码预测统计
        avg_red_hits = np.mean([r["evaluation"].get("red_hits", 0) for r in results])
        avg_blue_hits = np.mean([r["evaluation"].get("blue_hits", 0) for r in results])

        # 2+1命中统计
        hit_2_plus_1_count = sum(hit_2_plus_1_results)
        hit_2_plus_1_rate = (
            hit_2_plus_1_count / len(hit_2_plus_1_results)
            if hit_2_plus_1_results
            else 0
        )

        return {
            "total_periods": total,
            "red_odd_even_rate": red_odd_even_hits / total,
            "red_size_rate": red_size_hits / total,
            "blue_size_rate": blue_size_hits / total,
            "overall_state_rate": (red_odd_even_hits + red_size_hits + blue_size_hits)
            / (total * 3),
            "avg_red_hits": avg_red_hits,
            "avg_blue_hits": avg_blue_hits,
            "hit_2_plus_1_rate": hit_2_plus_1_rate,
            "hit_2_plus_1_count": hit_2_plus_1_count,
        }

    def _print_comparison_results(self, results: Dict, display_periods: int):
        """打印对比结果"""
        print("\n" + "=" * 60)
        print("📊 综合回测对比结果")
        print("=" * 60)

        # 创建对比表格
        modes = [mode for mode in results.keys() if "error" not in results[mode]]

        if not modes:
            print("❌ 没有成功的测试结果")
            return

        print(
            f"{'模式':<12} {'2+1命中率':<12} {'状态命中率':<12} {'红球平均':<10} {'蓝球平均':<10}"
        )
        print("-" * 60)

        best_2_plus_1 = 0
        best_mode = "basic"

        for mode in modes:
            stats = results[mode]["statistics"]
            hit_2_plus_1_rate = stats.get("hit_2_plus_1_rate", 0)
            state_rate = stats.get("overall_state_rate", 0)
            red_hits = stats.get("avg_red_hits", 0)
            blue_hits = stats.get("avg_blue_hits", 0)

            print(
                f"{mode.upper():<12} {hit_2_plus_1_rate:<12.1%} {state_rate:<12.1%} {red_hits:<10.2f} {blue_hits:<10.2f}"
            )

            if hit_2_plus_1_rate > best_2_plus_1:
                best_2_plus_1 = hit_2_plus_1_rate
                best_mode = mode

        print("\n🏆 最佳模式推荐:")
        print(f"   {best_mode.upper()} 模式 (2+1命中率: {best_2_plus_1:.1%})")

        # 显示详细结果示例
        if best_mode in results and display_periods > 0:
            print(f"\n📋 {best_mode.upper()} 模式详细结果示例:")
            best_results = results[best_mode]["results"][:display_periods]

            for result in best_results:
                period = result["period"]
                pred = result["prediction"]
                actual_red, actual_blue = result["actual"]
                eval_result = result["evaluation"]

                print(f"\n期号 {period}:")
                if "generated_numbers" in pred:
                    pred_red, pred_blue = pred["generated_numbers"]
                    print(
                        f"  预测: {format_numbers(pred_red)}——{format_numbers(pred_blue)}"
                    )
                    print(
                        f"  实际: {format_numbers(actual_red)}——{format_numbers(actual_blue)}"
                    )
                    print(
                        f"  命中: 红球{eval_result.get('red_hits', 0)}个, 蓝球{eval_result.get('blue_hits', 0)}个"
                    )
                    if eval_result.get("hit_2_plus_1", False):
                        print("  🎯 2+1命中!")

    def predict_and_display_next(self):
        """预测并显示下一期"""
        print("\n" + "=" * 60)
        print("🔮 预测下一期开奖号码")
        print("=" * 60)

        prediction = self.predict_next_period(0)

        print(
            f"预测模式: {prediction.get('prediction_mode', self.active_mode).upper()}"
        )
        print(f"预测时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        if "generated_numbers" in prediction:
            pred_red, pred_blue = prediction["generated_numbers"]
            print(
                f"\n🎯 预测号码: {format_numbers(pred_red)}——{format_numbers(pred_blue)}"
            )

        if "predictions" in prediction:
            preds = prediction["predictions"]
            print(f"\n📊 状态预测:")
            for key, (state, conf) in preds.items():
                print(f"  {key}: {state} (置信度: {conf:.3f})")

        if "confidence_scores" in prediction:
            print(f"\n🎯 置信度分析:")
            for key, score in prediction["confidence_scores"].items():
                print(f"  {key}: {score:.3f}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="大乐透终极预测系统")
    parser.add_argument(
        "--mode",
        choices=["basic", "enhanced", "super", "ultimate", "auto"],
        default="auto",
        help="预测模式",
    )
    parser.add_argument(
        "--action",
        choices=["predict", "backtest", "compare"],
        default="predict",
        help="执行动作",
    )
    parser.add_argument("--periods", type=int, default=10, help="回测期数")
    parser.add_argument("--display", type=int, default=10, help="显示期数")

    args = parser.parse_args()

    try:
        predictor = UltimateLotteryPredictor(mode=args.mode)

        if args.action == "predict":
            predictor.predict_and_display_next()
        elif args.action == "backtest":
            predictor.basic_predictor.run_backtest(
                num_periods=args.periods, display_periods=args.display
            )
        elif args.action == "compare":
            predictor.run_comprehensive_backtest(
                num_periods=args.periods, display_periods=args.display
            )

    except Exception as e:
        print(f"程序运行出错: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    main()
