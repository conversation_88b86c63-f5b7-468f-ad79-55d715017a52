"""
预测器适配器
将现有的预测器适配到新的回测框架
"""

import pandas as pd
from typing import List, Tuple
from .interfaces import PredictorInterface
from .data_models import PredictionResult


class LotteryPredictorAdapter(PredictorInterface):
    """
    LotteryPredictor 适配器
    将现有的 LotteryPredictor 适配到新的回测框架接口
    """
    
    def __init__(self, original_predictor):
        """
        初始化适配器
        
        Args:
            original_predictor: 原始的 LotteryPredictor 实例
        """
        self.original_predictor = original_predictor
        self.predictor_name = "LotteryPredictor"
        self.predictor_version = "1.0.0"
    
    def predict_for_period(self, data_index: int, data: pd.DataFrame) -> PredictionResult:
        """
        为指定数据索引预测下一期

        Args:
            data_index: 数据索引（不是期号！）
            data: 完整的历史数据

        Returns:
            PredictionResult: 标准化的预测结果
        """
        # 修复时间序列逻辑：数据现在按期号从旧到新排序
        # 更小的索引对应更早的期号，更大的索引对应更新的期号
        if data_index >= len(data) - 1:
            raise ValueError(f"数据索引 {data_index} 超出范围，无法获取训练数据")

        # 获取目标期号（我们要预测的期号）
        target_period_number = str(data.iloc[data_index]['期号'])

        # 训练数据：从数据开始到当前预测目标的所有历史数据
        # 这样可以模拟真实情况：预测时使用当前期及之前的所有历史数据
        train_data = data.iloc[:data_index + 1].copy()

        if len(train_data) == 0:
            raise ValueError(f"数据索引 {data_index} 没有足够的历史数据进行训练")

        # 保存原始数据
        original_data = self.original_predictor.data

        # 重要：原始预测器期望数据按期号降序排列，但我们的数据是升序的
        # 所以我们需要反转训练数据，使最新期在前，最旧期在后
        train_data_reversed = train_data.iloc[::-1].copy().reset_index(drop=True)

        # 临时设置训练数据（反转后的）
        self.original_predictor.data = train_data_reversed

        print(f"  训练数据: 使用当前期之后的所有历史数据，从第{data_index + 2}行到第{len(data)}行，共{len(train_data)}期数据")
        print(f"  使用改进预测器 - 置信度: 红球奇偶{self.original_predictor.improved_predictor.feature_predictability['red_odd_even']:.3f}, 红球大小{self.original_predictor.improved_predictor.feature_predictability['red_size']:.3f}, 蓝球{self.original_predictor.improved_predictor.feature_predictability['blue_size']:.3f}")

        # 调用原始预测器的预测方法
        # 现在数据已经反转，索引0是最新期（我们要预测的目标期）
        prediction_dict = self.original_predictor.predict_next_period(0)

        # 恢复原始数据
        self.original_predictor.data = original_data

        # 转换为标准格式
        return self._convert_to_standard_format(prediction_dict, target_period_number, data_index)
    
    def _convert_to_standard_format(self, prediction_dict: dict, period_number: str, data_index: int) -> PredictionResult:
        """将原始预测结果转换为标准格式"""
        
        # 提取比例预测
        red_odd_even_predictions = []
        red_size_predictions = []
        blue_size_predictions = []
        
        if 'predictions' in prediction_dict:
            predictions = prediction_dict['predictions']
            
            # 红球奇偶比
            if 'red_odd_even' in predictions:
                red_odd_even_list = predictions['red_odd_even']
                red_odd_even_predictions = self._normalize_prediction_list(red_odd_even_list)

            # 红球大小比
            if 'red_size' in predictions:
                red_size_list = predictions['red_size']
                red_size_predictions = self._normalize_prediction_list(red_size_list)

            # 蓝球大小比
            if 'blue_size' in predictions:
                blue_size_list = predictions['blue_size']
                blue_size_predictions = self._normalize_prediction_list(blue_size_list)
        
        # 提取生成的号码
        generated_numbers = ([], [])
        if 'generated_numbers' in prediction_dict:
            generated_numbers = prediction_dict['generated_numbers']
        
        # 提取杀号信息
        kill_numbers = {}
        if 'kill_numbers' in prediction_dict:
            kill_numbers = prediction_dict['kill_numbers']
        
        # 提取贝叶斯选择
        bayes_selected = None
        if 'bayes_selected' in prediction_dict:
            bayes_selected = prediction_dict['bayes_selected']
        
        # 提取所有组合
        all_combinations = None
        if 'all_combinations' in prediction_dict:
            all_combinations = prediction_dict['all_combinations']
        
        return PredictionResult(
            period_number=period_number,
            data_index=data_index,
            red_odd_even_predictions=red_odd_even_predictions,
            red_size_predictions=red_size_predictions,
            blue_size_predictions=blue_size_predictions,
            generated_numbers=generated_numbers,
            kill_numbers=kill_numbers,
            bayes_selected=bayes_selected,
            all_combinations=all_combinations,
            predictor_name=self.predictor_name,
            training_data_size=len(self.original_predictor.data)
        )
    
    def get_predictor_name(self) -> str:
        """获取预测器名称"""
        return self.predictor_name
    
    def get_predictor_version(self) -> str:
        """获取预测器版本"""
        return self.predictor_version

    def _normalize_prediction_list(self, prediction_data) -> List[Tuple[str, float]]:
        """
        标准化预测数据格式，确保返回List[Tuple[str, float]]

        Args:
            prediction_data: 可能是单个元组或元组列表

        Returns:
            List[Tuple[str, float]]: 标准化的预测列表
        """
        if not prediction_data:
            return []

        # 如果是单个元组格式 ('3:2', 0.5)
        if isinstance(prediction_data, (list, tuple)) and len(prediction_data) == 2:
            if isinstance(prediction_data[0], str) and isinstance(prediction_data[1], (int, float)):
                # 这是单个预测结果，转换为列表格式
                return [prediction_data]

        # 如果已经是列表格式 [('3:2', 0.5), ('2:3', 0.3)]
        if isinstance(prediction_data, list):
            return [(pred, prob) for pred, prob in prediction_data]

        # 回退：尝试直接转换
        try:
            return [(pred, prob) for pred, prob in prediction_data]
        except:
            # 最后的回退：返回空列表
            return []


class AdvancedProbabilisticSystemAdapter(PredictorInterface):
    """
    AdvancedProbabilisticSystem 适配器
    """
    
    def __init__(self, original_system):
        """
        初始化适配器
        
        Args:
            original_system: 原始的 AdvancedProbabilisticSystem 实例
        """
        self.original_system = original_system
        self.predictor_name = "AdvancedProbabilisticSystem"
        self.predictor_version = "2.0.0"
    
    def predict_for_period(self, data_index: int, data: pd.DataFrame) -> PredictionResult:
        """为指定数据索引预测下一期"""
        period_number = str(data.iloc[data_index]['期号'])

        # 修复时间序列逻辑：确保AdvancedProbabilisticSystem使用正确的数据
        # 由于该系统内部有自己的数据处理逻辑，我们需要确保它使用正确的数据
        if data_index >= len(data) - 1:
            raise ValueError(f"数据索引 {data_index} 超出范围，无法获取训练数据")

        # 临时更新系统的数据为完整数据，让系统内部处理时间序列逻辑
        self.original_system.data = data

        print(f"  📊 AdvancedProbabilisticSystem 将内部处理期号 {period_number} 的时间序列逻辑")

        # 调用原始系统的预测方法
        kill_result = self.original_system.predict_kills_by_period(
            period_number=period_number,
            red_target_count=5,
            blue_target_count=1
        )
        
        # 转换为标准格式
        kill_numbers = {}
        generated_numbers = ([], [])  # 该系统主要做杀号，不生成号码
        
        if kill_result['success']:
            kill_numbers = {
                'red_universal': kill_result['red_kills'],
                'blue_universal': kill_result['blue_kills']
            }
        
        return PredictionResult(
            period_number=period_number,
            data_index=data_index,
            red_odd_even_predictions=[],  # 该系统不做比例预测
            red_size_predictions=[],
            blue_size_predictions=[],
            generated_numbers=generated_numbers,
            kill_numbers=kill_numbers,
            predictor_name=self.predictor_name,
            training_data_size=kill_result.get('train_data_periods', 0)
        )
    
    def get_predictor_name(self) -> str:
        """获取预测器名称"""
        return self.predictor_name
    
    def get_predictor_version(self) -> str:
        """获取预测器版本"""
        return self.predictor_version


class ImprovedPredictorAdapter(PredictorInterface):
    """
    ImprovedPredictor 适配器
    将 ImprovedPredictor 适配到新的回测框架接口
    """

    def __init__(self, original_predictor):
        """
        初始化适配器

        Args:
            original_predictor: 原始的 ImprovedPredictor 实例
        """
        self.original_predictor = original_predictor
        self.predictor_name = "ImprovedPredictor"
        self.predictor_version = "2.0.0"

    def predict_for_period(self, data_index: int, data: pd.DataFrame) -> PredictionResult:
        """
        为指定数据索引预测下一期

        Args:
            data_index: 数据索引（不是期号！）
            data: 完整的历史数据

        Returns:
            PredictionResult: 标准化的预测结果
        """
        # 修复时间序列逻辑：数据现在按期号从旧到新排序
        # 更小的索引对应更早的期号，更大的索引对应更新的期号
        if data_index >= len(data) - 1:
            raise ValueError(f"数据索引 {data_index} 超出范围，无法获取训练数据")

        # 训练数据：从数据开始到当前预测目标的所有历史数据
        train_data = data.iloc[data_index + 1:].copy()

        if len(train_data) == 0:
            raise ValueError(f"数据索引 {data_index} 没有足够的历史数据进行训练")

        self.original_predictor.data = train_data

        # 获取要预测的期号
        target_period = data.iloc[data_index]['期号']
        period_number = str(target_period)

        # 显示训练数据范围（注意：train_data也是按期号降序排列）
        newest_train_period = train_data.iloc[0]['期号']  # 训练数据中最新的期号
        oldest_train_period = train_data.iloc[-1]['期号']  # 训练数据中最老的期号
        print(f"  📊 使用 {len(train_data)} 期历史数据训练（期号 {oldest_train_period} 到 {newest_train_period}）")
        print(f"  🎯 预测目标：期号 {target_period}")

        # 调用原始预测器的预测方法
        # 注意：predict_with_insights(0) 表示预测训练数据的下一期
        prediction_dict = self.original_predictor.predict_with_insights(0)

        # 转换为标准格式
        return self._convert_to_standard_format(prediction_dict, period_number, data_index)

    def _convert_to_standard_format(self, prediction_dict: dict, period_number: str, data_index: int) -> PredictionResult:
        """将原始预测结果转换为标准格式"""

        # 提取比例预测
        red_odd_even_predictions = []
        red_size_predictions = []
        blue_size_predictions = []

        if 'predictions' in prediction_dict:
            predictions = prediction_dict['predictions']

            # 红球奇偶比
            if 'red_odd_even' in predictions:
                red_odd_even_list = predictions['red_odd_even']
                red_odd_even_predictions = self._normalize_prediction_list(red_odd_even_list)

            # 红球大小比
            if 'red_size' in predictions:
                red_size_list = predictions['red_size']
                red_size_predictions = self._normalize_prediction_list(red_size_list)

            # 蓝球大小比
            if 'blue_size' in predictions:
                blue_size_list = predictions['blue_size']
                blue_size_predictions = self._normalize_prediction_list(blue_size_list)

        # 提取生成的号码
        generated_numbers = ([], [])
        if 'generated_numbers' in prediction_dict:
            generated_numbers = prediction_dict['generated_numbers']

        return PredictionResult(
            period_number=period_number,
            data_index=data_index,
            red_odd_even_predictions=red_odd_even_predictions,
            red_size_predictions=red_size_predictions,
            blue_size_predictions=blue_size_predictions,
            generated_numbers=generated_numbers,
            kill_numbers=prediction_dict.get('kill_numbers', {}),
            predictor_name=self.predictor_name,
            training_data_size=len(self.original_predictor.data)
        )

    def _normalize_prediction_list(self, prediction_list):
        """标准化预测列表格式"""
        if not prediction_list:
            return []

        normalized = []
        for item in prediction_list:
            if isinstance(item, (list, tuple)) and len(item) >= 2:
                # 格式：(状态, 概率)
                normalized.append((item[0], float(item[1])))
            elif isinstance(item, str):
                # 只有状态，默认概率
                normalized.append((item, 0.5))
            else:
                # 其他格式，尝试转换
                normalized.append((str(item), 0.5))

        return normalized

    def get_predictor_name(self) -> str:
        """获取预测器名称"""
        return self.predictor_name

    def get_predictor_version(self) -> str:
        """获取预测器版本"""
        return self.predictor_version


def create_predictor_adapter(predictor_type: str, original_predictor):
    """
    工厂方法：创建预测器适配器

    Args:
        predictor_type: 预测器类型 ('lottery', 'improved', 'advanced', 'ultimate')
        original_predictor: 原始预测器实例

    Returns:
        PredictorInterface: 适配后的预测器
    """
    if predictor_type == 'lottery':
        return LotteryPredictorAdapter(original_predictor)
    elif predictor_type == 'improved':
        return ImprovedPredictorAdapter(original_predictor)
    elif predictor_type == 'advanced':
        return AdvancedProbabilisticSystemAdapter(original_predictor)
    elif predictor_type == 'lstm':
        return LSTMPredictorAdapter(original_predictor)
    else:
        raise ValueError(f"不支持的预测器类型: {predictor_type}")


class LSTMPredictorAdapter(PredictorInterface):
    """LSTM预测器适配器"""

    def __init__(self, data: pd.DataFrame):
        """
        初始化LSTM适配器

        Args:
            data: 历史数据DataFrame
        """
        self.data = data
        self.predictor_name = "LSTM深度学习预测器"
        self.predictor_version = "1.0.0"
        self.lstm_predictor = None
        self._initialize_lstm()

    def _initialize_lstm(self):
        """初始化LSTM预测器"""
        try:
            from src.models.deep_learning.lstm_predictor import LSTMPredictor, LSTMConfig

            # 配置LSTM参数
            config = LSTMConfig(
                lstm_units=64,
                lstm_layers=2,
                dense_units=32,
                dropout_rate=0.2,
                sequence_length=10,
                learning_rate=0.001,
                epochs=30,
                batch_size=16,
                validation_split=0.2
            )

            self.lstm_predictor = LSTMPredictor(config)

        except ImportError as e:
            print(f"⚠️ LSTM预测器导入失败: {e}")
            self.lstm_predictor = None

    def predict_for_period(self, data_index: int, data: pd.DataFrame) -> PredictionResult:
        """为指定期数进行预测"""
        if data_index >= len(data) - 1:
            raise ValueError(f"数据索引 {data_index} 超出范围")

        # 获取期号
        period_number = str(data.iloc[data_index]['期号'])

        try:
            # 准备训练数据
            train_data = data.iloc[:data_index + 1].copy()
            features_df = self._prepare_features(train_data)

            if len(features_df) < 20:  # 数据不足
                return self._create_fallback_prediction(data_index, period_number)

            # 使用LSTM进行比例预测
            red_odd_even_pred = self._lstm_ratio_prediction(features_df, 'red_odd_even')
            red_size_pred = self._lstm_ratio_prediction(features_df, 'red_size')
            blue_size_pred = self._lstm_ratio_prediction(features_df, 'blue_size')

            # 使用LSTM进行杀号预测
            red_kills = self._lstm_kill_numbers(features_df, 'red')
            blue_kills = self._lstm_kill_numbers(features_df, 'blue')

            # 生成号码（简单示例）
            red_numbers = [i for i in range(1, 36) if i not in red_kills][:5]
            blue_numbers = [i for i in range(1, 13) if i not in blue_kills][:2]

            return PredictionResult(
                period_number=period_number,
                data_index=data_index,
                red_odd_even_predictions=[(red_odd_even_pred, 0.8)],
                red_size_predictions=[(red_size_pred, 0.8)],
                blue_size_predictions=[(blue_size_pred, 0.8)],
                generated_numbers=(red_numbers, blue_numbers),
                kill_numbers={'red_kills': red_kills, 'blue_kills': blue_kills},
                predictor_name=self.predictor_name,
                training_data_size=len(features_df)
            )

        except Exception as e:
            print(f"⚠️ LSTM预测失败: {e}")
            return self._create_fallback_prediction(data_index, period_number)

    def _prepare_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """准备LSTM特征数据"""
        features = []

        for _, row in data.iterrows():
            red_numbers, blue_numbers = self._parse_numbers(row)

            if len(red_numbers) >= 5 and len(blue_numbers) >= 2:
                feature_row = {
                    '期号': row['期号'],
                    # 红球特征
                    'red_sum': sum(red_numbers),
                    'red_mean': sum(red_numbers) / len(red_numbers),
                    'red_std': self._calculate_std(red_numbers),
                    'red_min': min(red_numbers),
                    'red_max': max(red_numbers),
                    'red_range': max(red_numbers) - min(red_numbers),
                    'red_odd_count': sum(1 for num in red_numbers if num % 2 == 1),
                    'red_even_count': sum(1 for num in red_numbers if num % 2 == 0),
                    'red_big_count': sum(1 for num in red_numbers if num > 17),
                    'red_small_count': sum(1 for num in red_numbers if num <= 17),
                    # 蓝球特征
                    'blue_sum': sum(blue_numbers),
                    'blue_mean': sum(blue_numbers) / len(blue_numbers),
                    'blue_min': min(blue_numbers),
                    'blue_max': max(blue_numbers),
                    'blue_big_count': sum(1 for num in blue_numbers if num > 6),
                    'blue_small_count': sum(1 for num in blue_numbers if num <= 6),
                    # 跨度特征
                    'red_span': max(red_numbers) - min(red_numbers),
                    'blue_span': max(blue_numbers) - min(blue_numbers) if len(blue_numbers) > 1 else 0
                }
                features.append(feature_row)

        return pd.DataFrame(features)

    def _parse_numbers(self, row: pd.Series) -> tuple:
        """解析红球和蓝球数字"""
        red_numbers = []
        for i in range(1, 6):
            col_name = f'红球{i}'
            if col_name in row and pd.notna(row[col_name]):
                red_numbers.append(int(row[col_name]))

        blue_numbers = []
        for i in range(1, 3):
            col_name = f'蓝球{i}'
            if col_name in row and pd.notna(row[col_name]):
                blue_numbers.append(int(row[col_name]))

        return red_numbers, blue_numbers

    def _calculate_std(self, numbers: list) -> float:
        """计算标准差"""
        if len(numbers) <= 1:
            return 0.0
        mean = sum(numbers) / len(numbers)
        variance = sum((x - mean) ** 2 for x in numbers) / len(numbers)
        return variance ** 0.5

    def _lstm_ratio_prediction(self, features_df: pd.DataFrame, ratio_type: str) -> str:
        """使用LSTM预测比例"""
        if self.lstm_predictor is None:
            return self._fallback_ratio_prediction(features_df, ratio_type)

        try:
            # 根据比例类型选择目标列
            if ratio_type == 'red_odd_even':
                target_col = 'red_odd_count'
            elif ratio_type == 'red_size':
                target_col = 'red_big_count'
            elif ratio_type == 'blue_size':
                target_col = 'blue_big_count'
            else:
                return "2:3"

            # 训练模型
            train_result = self.lstm_predictor.train(features_df, target_col)

            if not train_result.get('error'):
                # 进行预测
                pred_result = self.lstm_predictor.predict(features_df, len(features_df) - 1)

                if pred_result.metadata.get('success'):
                    pred_value = pred_result.value

                    # 将预测值转换为比例
                    if ratio_type == 'red_odd_even':
                        odd_count = max(0, min(5, round(pred_value)))
                        even_count = 5 - odd_count
                        return f"{odd_count}:{even_count}"
                    elif ratio_type == 'red_size':
                        big_count = max(0, min(5, round(pred_value)))
                        small_count = 5 - big_count
                        return f"{big_count}:{small_count}"  # 大数:小数格式
                    elif ratio_type == 'blue_size':
                        big_count = max(0, min(2, round(pred_value)))
                        small_count = 2 - big_count
                        return f"{big_count}:{small_count}"  # 大数:小数格式

            return self._fallback_ratio_prediction(features_df, ratio_type)

        except Exception as e:
            print(f"⚠️ LSTM比例预测失败: {e}")
            return self._fallback_ratio_prediction(features_df, ratio_type)

    def _lstm_kill_numbers(self, features_df: pd.DataFrame, ball_type: str) -> list:
        """使用LSTM预测杀号"""
        if self.lstm_predictor is None:
            return self._fallback_kill_numbers(ball_type)

        try:
            if ball_type == 'red':
                target_cols = ['red_sum', 'red_mean']
                max_num = 35
                kill_count = 3
            else:
                target_cols = ['blue_sum', 'blue_mean']
                max_num = 12
                kill_count = 1

            kill_candidates = []

            for target_col in target_cols:
                train_result = self.lstm_predictor.train(features_df, target_col)

                if not train_result.get('error'):
                    pred_result = self.lstm_predictor.predict(features_df, len(features_df) - 1)

                    if pred_result.metadata.get('success'):
                        pred_value = pred_result.value

                        # 基于预测值生成杀号候选
                        if ball_type == 'red':
                            if target_col == 'red_sum':
                                avg_per_ball = pred_value / 5
                                candidates = [i for i in range(1, 36) if abs(i - avg_per_ball) > 10]
                            else:  # red_mean
                                candidates = [i for i in range(1, 36) if abs(i - pred_value) > 8]
                        else:  # blue
                            if target_col == 'blue_sum':
                                avg_per_ball = pred_value / 2
                                candidates = [i for i in range(1, 13) if abs(i - avg_per_ball) > 3]
                            else:  # blue_mean
                                candidates = [i for i in range(1, 13) if abs(i - pred_value) > 2]

                        kill_candidates.extend(candidates[:3])

            # 选择最频繁的杀号
            if kill_candidates:
                from collections import Counter
                kill_counts = Counter(kill_candidates)
                final_kills = [num for num, _ in kill_counts.most_common(kill_count)]
                return final_kills

            return self._fallback_kill_numbers(ball_type)

        except Exception as e:
            print(f"⚠️ LSTM杀号失败: {e}")
            return self._fallback_kill_numbers(ball_type)

    def _fallback_ratio_prediction(self, features_df: pd.DataFrame, ratio_type: str) -> str:
        """回退比例预测方法"""
        recent_data = features_df.tail(10)

        if ratio_type == 'red_odd_even':
            odd_counts = recent_data['red_odd_count'].tolist()
            avg_odd = sum(odd_counts) / len(odd_counts)
            odd_count = max(0, min(5, round(avg_odd)))
            return f"{odd_count}:{5-odd_count}"
        elif ratio_type == 'red_size':
            big_counts = recent_data['red_big_count'].tolist()
            avg_big = sum(big_counts) / len(big_counts)
            big_count = max(0, min(5, round(avg_big)))
            return f"{big_count}:{5-big_count}"  # 大数:小数格式
        elif ratio_type == 'blue_size':
            big_counts = recent_data['blue_big_count'].tolist()
            avg_big = sum(big_counts) / len(big_counts)
            big_count = max(0, min(2, round(avg_big)))
            return f"{big_count}:{2-big_count}"  # 大数:小数格式

        return "2:3"

    def _fallback_kill_numbers(self, ball_type: str) -> list:
        """回退杀号方法"""
        if ball_type == 'red':
            return [1, 2, 35]  # 默认红球杀号
        else:
            return [12]  # 默认蓝球杀号

    def _create_fallback_prediction(self, data_index: int, period_number: str) -> PredictionResult:
        """创建回退预测结果"""
        return PredictionResult(
            period_number=period_number,
            data_index=data_index,
            red_odd_even_predictions=[("2:3", 0.5)],
            red_size_predictions=[("2:3", 0.5)],
            blue_size_predictions=[("1:1", 0.5)],
            generated_numbers=([1, 2, 3, 4, 5], [1, 2]),
            kill_numbers={'red_kills': [35], 'blue_kills': [12]},
            predictor_name=self.predictor_name,
            training_data_size=0
        )

    def get_predictor_name(self) -> str:
        """获取预测器名称"""
        return self.predictor_name

    def get_predictor_version(self) -> str:
        """获取预测器版本"""
        return self.predictor_version


# 使用示例
def example_usage():
    """使用示例"""
    from systems.main import LotteryPredictor
    from framework import BacktestFramework, BacktestConfig
    
    # 创建原始预测器
    original_predictor = LotteryPredictor()
    
    # 创建适配器
    predictor_adapter = create_predictor_adapter('lottery', original_predictor)
    
    # 创建回测框架
    framework = BacktestFramework(original_predictor.data)
    
    # 配置回测
    config = BacktestConfig(
        num_periods=10,
        min_train_periods=50,
        display_periods=5
    )
    
    # 运行回测
    result = framework.run_backtest(predictor_adapter, config)
    
    # 显示结果
    print(f"回测完成：{result.get_success_rate():.1%} 成功率")
    print(f"2+1命中率：{result.statistics.hit_2_plus_1_rate:.1%}")
    
    return result
