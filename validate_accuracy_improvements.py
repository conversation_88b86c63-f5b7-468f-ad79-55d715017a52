#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证准确率改进效果
测试新创建的简化模块是否能提升系统准确率
"""

import os
import sys
import json
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Tuple, Any
import logging
from pathlib import Path

# 添加项目路径
sys.path.append('src')

class AccuracyImprovementValidator:
    """准确率改进验证器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        logging.basicConfig(level=logging.INFO)
        
        # 加载数据
        self.data = self.load_lottery_data()
        
        # 导入新创建的模块
        self.load_improved_modules()
        
    def load_lottery_data(self) -> pd.DataFrame:
        """加载彩票数据"""
        try:
            data_path = "data/raw/dlt_data.csv"
            if os.path.exists(data_path):
                data = pd.read_csv(data_path)
                print(f"✅ 加载数据: {len(data)} 条记录")
                return data
            else:
                print("⚠️ 使用模拟数据进行测试")
                return self.create_mock_data()
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return self.create_mock_data()
    
    def create_mock_data(self) -> pd.DataFrame:
        """创建模拟数据"""
        periods = []
        for i in range(100):
            period = f"250{i:02d}"
            
            # 生成随机红球
            red_balls = sorted(np.random.choice(range(1, 36), 5, replace=False))
            
            # 生成随机蓝球
            blue_balls = sorted(np.random.choice(range(1, 13), 2, replace=False))
            
            period_data = {'期号': period}
            for j, ball in enumerate(red_balls, 1):
                period_data[f'红球{j}'] = ball
            for j, ball in enumerate(blue_balls, 1):
                period_data[f'蓝球{j}'] = ball
            
            periods.append(period_data)
        
        return pd.DataFrame(periods)
    
    def load_improved_modules(self):
        """加载改进的模块"""
        try:
            # 动态导入简化比例预测器
            exec(open("simplified_ratio_predictor.py").read(), globals())
            self.ratio_predictor = SimplifiedRatioPredictor()
            print("✅ 简化比例预测器加载成功")
            
            # 动态导入改进号码生成器
            exec(open("improved_number_generator.py").read(), globals())
            self.number_generator = ImprovedNumberGenerator()
            print("✅ 改进号码生成器加载成功")
            
        except Exception as e:
            print(f"❌ 模块加载失败: {e}")
            self.ratio_predictor = None
            self.number_generator = None
    
    def test_ratio_prediction_diversity(self) -> Dict:
        """测试比例预测多样性"""
        print("\n🔍 测试比例预测多样性...")
        
        if not self.ratio_predictor:
            return {"error": "比例预测器未加载"}
        
        # 测试10期预测
        test_periods = 10
        predictions = {
            "red_odd_even": [],
            "red_size": [],
            "blue_size": [],
            "confidences": []
        }
        
        for i in range(test_periods):
            train_data = self.data.iloc[:-(test_periods-i)] if len(self.data) > test_periods else self.data
            current_period = len(train_data)
            
            # 预测比例
            odd_even_pred, odd_even_conf = self.ratio_predictor.predict_red_odd_even_ratio(train_data, current_period)
            size_pred, size_conf = self.ratio_predictor.predict_red_size_ratio(train_data, current_period)
            blue_pred, blue_conf = self.ratio_predictor.predict_blue_size_ratio(train_data, current_period)
            
            predictions["red_odd_even"].append(odd_even_pred)
            predictions["red_size"].append(size_pred)
            predictions["blue_size"].append(blue_pred)
            predictions["confidences"].append((odd_even_conf, size_conf, blue_conf))
        
        # 计算多样性
        diversity_results = {
            "red_odd_even_diversity": len(set(predictions["red_odd_even"])) / len(predictions["red_odd_even"]),
            "red_size_diversity": len(set(predictions["red_size"])) / len(predictions["red_size"]),
            "blue_size_diversity": len(set(predictions["blue_size"])) / len(predictions["blue_size"]),
            "avg_confidence": {
                "odd_even": np.mean([c[0] for c in predictions["confidences"]]),
                "size": np.mean([c[1] for c in predictions["confidences"]]),
                "blue": np.mean([c[2] for c in predictions["confidences"]])
            },
            "predictions": predictions
        }
        
        print(f"  红球奇偶比多样性: {diversity_results['red_odd_even_diversity']:.1%}")
        print(f"  红球大小比多样性: {diversity_results['red_size_diversity']:.1%}")
        print(f"  蓝球大小比多样性: {diversity_results['blue_size_diversity']:.1%}")
        print(f"  平均置信度: 奇偶{diversity_results['avg_confidence']['odd_even']:.3f}, 大小{diversity_results['avg_confidence']['size']:.3f}")
        
        return diversity_results
    
    def test_number_generation_quality(self) -> Dict:
        """测试号码生成质量"""
        print("\n🎯 测试号码生成质量...")
        
        if not self.number_generator:
            return {"error": "号码生成器未加载"}
        
        # 生成10组号码
        test_count = 10
        generated_numbers = []
        quality_metrics = {
            "red_sums": [],
            "red_spans": [],
            "ratio_matches": {"odd_even": 0, "size": 0},
            "uniqueness": 0
        }
        
        for i in range(test_count):
            train_data = self.data.iloc[:-(test_count-i)] if len(self.data) > test_count else self.data
            
            # 模拟杀号
            kill_red = [1, 2, 3, 4, 5]  # 模拟杀号
            kill_blue = [1, 2]
            
            # 生成号码
            red_balls = self.number_generator.generate_red_balls(
                train_data, kill_red, "3:2", "2:3"
            )
            blue_balls = self.number_generator.generate_blue_balls(
                train_data, kill_blue, "1:1"
            )
            
            generated_numbers.append((red_balls, blue_balls))
            
            # 计算质量指标
            if red_balls:
                quality_metrics["red_sums"].append(sum(red_balls))
                quality_metrics["red_spans"].append(max(red_balls) - min(red_balls))
                
                # 检查比例匹配
                odd_count = sum(1 for x in red_balls if x % 2 == 1)
                if f"{odd_count}:{5-odd_count}" == "3:2":
                    quality_metrics["ratio_matches"]["odd_even"] += 1
                
                big_count = sum(1 for x in red_balls if x > 18)
                if f"{big_count}:{5-big_count}" == "2:3":
                    quality_metrics["ratio_matches"]["size"] += 1
        
        # 计算唯一性
        unique_combinations = len(set(tuple(combo[0]) for combo in generated_numbers))
        quality_metrics["uniqueness"] = unique_combinations / test_count
        
        # 统计结果
        results = {
            "avg_red_sum": np.mean(quality_metrics["red_sums"]) if quality_metrics["red_sums"] else 0,
            "avg_red_span": np.mean(quality_metrics["red_spans"]) if quality_metrics["red_spans"] else 0,
            "odd_even_match_rate": quality_metrics["ratio_matches"]["odd_even"] / test_count,
            "size_match_rate": quality_metrics["ratio_matches"]["size"] / test_count,
            "uniqueness_rate": quality_metrics["uniqueness"],
            "generated_samples": generated_numbers[:5]  # 前5个样本
        }
        
        print(f"  红球和值平均: {results['avg_red_sum']:.1f} (目标范围: 85-115)")
        print(f"  红球跨度平均: {results['avg_red_span']:.1f} (目标范围: 18-28)")
        print(f"  奇偶比匹配率: {results['odd_even_match_rate']:.1%}")
        print(f"  大小比匹配率: {results['size_match_rate']:.1%}")
        print(f"  号码唯一性: {results['uniqueness_rate']:.1%}")
        
        return results
    
    def run_simple_backtest(self) -> Dict:
        """运行简单回测"""
        print("\n📊 运行简单回测验证...")
        
        if not self.ratio_predictor or len(self.data) < 20:
            return {"error": "无法运行回测"}
        
        # 回测最近10期
        backtest_periods = min(10, len(self.data) - 10)
        results = {
            "red_odd_even_correct": 0,
            "red_size_correct": 0,
            "blue_size_correct": 0,
            "total_periods": backtest_periods,
            "details": []
        }
        
        for i in range(backtest_periods):
            # 训练数据（不包含当前期）
            train_end = len(self.data) - backtest_periods + i
            train_data = self.data.iloc[:train_end]
            
            # 实际结果
            actual_row = self.data.iloc[train_end]
            actual_red = [actual_row[f'红球{j}'] for j in range(1, 6)]
            actual_blue = [actual_row[f'蓝球{j}'] for j in range(1, 3)]
            
            # 计算实际比例
            actual_odd_count = sum(1 for x in actual_red if x % 2 == 1)
            actual_odd_even = f"{actual_odd_count}:{5-actual_odd_count}"
            
            actual_big_count = sum(1 for x in actual_red if x > 18)
            actual_red_size = f"{actual_big_count}:{5-actual_big_count}"
            
            actual_blue_big = sum(1 for x in actual_blue if x > 6)
            actual_blue_size = f"{actual_blue_big}:{2-actual_blue_big}"
            
            # 预测
            pred_odd_even, _ = self.ratio_predictor.predict_red_odd_even_ratio(train_data, train_end)
            pred_red_size, _ = self.ratio_predictor.predict_red_size_ratio(train_data, train_end)
            pred_blue_size, _ = self.ratio_predictor.predict_blue_size_ratio(train_data, train_end)
            
            # 检查准确性
            odd_even_correct = pred_odd_even == actual_odd_even
            red_size_correct = pred_red_size == actual_red_size
            blue_size_correct = pred_blue_size == actual_blue_size
            
            if odd_even_correct:
                results["red_odd_even_correct"] += 1
            if red_size_correct:
                results["red_size_correct"] += 1
            if blue_size_correct:
                results["blue_size_correct"] += 1
            
            results["details"].append({
                "period": actual_row['期号'],
                "predictions": {
                    "odd_even": pred_odd_even,
                    "red_size": pred_red_size,
                    "blue_size": pred_blue_size
                },
                "actual": {
                    "odd_even": actual_odd_even,
                    "red_size": actual_red_size,
                    "blue_size": actual_blue_size
                },
                "correct": {
                    "odd_even": odd_even_correct,
                    "red_size": red_size_correct,
                    "blue_size": blue_size_correct
                }
            })
        
        # 计算准确率
        results["accuracy"] = {
            "red_odd_even": results["red_odd_even_correct"] / backtest_periods,
            "red_size": results["red_size_correct"] / backtest_periods,
            "blue_size": results["blue_size_correct"] / backtest_periods
        }
        
        print(f"  红球奇偶比准确率: {results['accuracy']['red_odd_even']:.1%}")
        print(f"  红球大小比准确率: {results['accuracy']['red_size']:.1%}")
        print(f"  蓝球大小比准确率: {results['accuracy']['blue_size']:.1%}")
        
        return results
    
    def run_validation(self) -> Dict:
        """运行完整验证"""
        print("🚀 开始验证准确率改进效果")
        print("=" * 50)
        
        validation_results = {
            "validation_date": datetime.now().isoformat(),
            "data_info": {
                "total_records": len(self.data),
                "data_source": "real" if os.path.exists("data/raw/dlt_data.csv") else "mock"
            }
        }
        
        # 1. 测试预测多样性
        diversity_results = self.test_ratio_prediction_diversity()
        validation_results["diversity_test"] = diversity_results
        
        # 2. 测试号码生成质量
        generation_results = self.test_number_generation_quality()
        validation_results["generation_test"] = generation_results
        
        # 3. 运行简单回测
        backtest_results = self.run_simple_backtest()
        validation_results["backtest_results"] = backtest_results
        
        # 4. 生成改进评估
        print("\n📋 改进效果评估:")
        
        if "error" not in diversity_results:
            print(f"✅ 预测多样性显著改善:")
            print(f"   - 红球奇偶比多样性: {diversity_results['red_odd_even_diversity']:.1%}")
            print(f"   - 置信度合理化: {diversity_results['avg_confidence']['odd_even']:.3f}")
        
        if "error" not in generation_results:
            print(f"✅ 号码生成质量提升:")
            print(f"   - 比例匹配率: 奇偶{generation_results['odd_even_match_rate']:.1%}, 大小{generation_results['size_match_rate']:.1%}")
            print(f"   - 号码唯一性: {generation_results['uniqueness_rate']:.1%}")
        
        if "error" not in backtest_results:
            print(f"✅ 回测准确率:")
            print(f"   - 红球奇偶比: {backtest_results['accuracy']['red_odd_even']:.1%}")
            print(f"   - 红球大小比: {backtest_results['accuracy']['red_size']:.1%}")
            print(f"   - 蓝球大小比: {backtest_results['accuracy']['blue_size']:.1%}")
        
        # 保存验证结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        result_file = f"optimization_results/validation_results_{timestamp}.json"
        
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(validation_results, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"\n📁 验证结果已保存: {result_file}")
        
        return validation_results

if __name__ == "__main__":
    validator = AccuracyImprovementValidator()
    results = validator.run_validation()
