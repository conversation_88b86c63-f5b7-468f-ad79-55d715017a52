#!/usr/bin/env python3
"""快速测试深度学习模型修复"""

from src.models.deep_learning.lstm_predictor import LSTMPredictor
from src.models.deep_learning.transformer_predictor import TransformerPredictor
import pandas as pd
import numpy as np

print('🔬 快速测试深度学习模型修复...')

# 创建测试数据
data = pd.DataFrame(np.random.rand(50, 21))
data.columns = [f'col_{i}' for i in range(21)]

# 测试LSTM
print('📊 测试LSTM...')
lstm = LSTMPredictor()
result = lstm.predict(data, 20)
print(f'✅ LSTM预测结果类型: {type(result).__name__}')
print(f'✅ LSTM有metadata: {hasattr(result, "metadata")}')
if hasattr(result, 'metadata'):
    print(f'✅ LSTM metadata内容: {result.metadata.keys()}')

# 测试Transformer  
print('🔄 测试Transformer...')
transformer = TransformerPredictor()
result = transformer.predict(data, 20)
print(f'✅ Transformer预测结果类型: {type(result).__name__}')
print(f'✅ Transformer有metadata: {hasattr(result, "metadata")}')
if hasattr(result, 'metadata'):
    print(f'✅ Transformer metadata内容: {result.metadata.keys()}')

print('🎉 PredictionResult修复验证完成！')
