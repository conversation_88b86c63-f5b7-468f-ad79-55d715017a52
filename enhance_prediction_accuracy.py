#!/usr/bin/env python3
"""
预测准确率提升脚本
Prediction Accuracy Enhancement Script

通过集成学习、模型融合等技术，将杀号成功率提升到95%以上
"""

import sys
from pathlib import Path
import pandas as pd
import numpy as np
import time
import logging
import json
from typing import Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/accuracy_enhancement.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

def load_lottery_data():
    """加载彩票数据"""
    print("📊 加载彩票数据...")
    
    data_path = Path("data/raw/dlt_data.csv")
    if data_path.exists():
        data = pd.read_csv(data_path)
        print(f"✅ 加载数据: {len(data)} 条记录")
        return data
    else:
        print("⚠️ 数据文件不存在，使用模拟数据")
        return create_mock_data()

def create_mock_data():
    """创建模拟数据用于测试"""
    print("🎲 创建模拟彩票数据...")
    
    periods = []
    for i in range(1000, 1100):  # 100期数据
        period = f"250{i:02d}"
        
        # 生成随机红球 (1-35选5)
        red_balls = sorted(np.random.choice(range(1, 36), 5, replace=False))
        red_str = ' '.join(map(str, red_balls))
        
        # 生成随机蓝球 (1-12选2)
        blue_balls = sorted(np.random.choice(range(1, 13), 2, replace=False))
        blue_str = ' '.join(map(str, blue_balls))
        
        periods.append({
            '期号': period,
            '红球': red_str,
            '蓝球': blue_str
        })
    
    return pd.DataFrame(periods)

def test_ensemble_predictor():
    """测试集成预测器"""
    print("\n🔮 测试集成预测器...")
    
    try:
        from src.optimization.accuracy_enhancer import EnsemblePredictor, AccuracyConfig
        
        # 创建配置
        config = AccuracyConfig(
            target_kill_success_rate=0.95,
            ensemble_size=3,
            fusion_method='adaptive'
        )
        
        # 创建集成预测器
        ensemble = EnsemblePredictor(config)
        
        # 加载数据
        data = load_lottery_data()
        
        print("🚀 执行集成预测测试...")
        
        # 测试预测
        test_results = []
        test_periods = 5  # 测试5期
        
        for i in range(len(data) - test_periods, len(data)):
            try:
                period = str(data.iloc[i]['期号'])
                
                # 执行预测
                prediction = ensemble.ensemble_predict(data, i)
                
                # 解析实际结果
                actual_red = [int(x) for x in str(data.iloc[i]['红球']).split()]
                actual_blue = [int(x) for x in str(data.iloc[i]['蓝球']).split()]
                actual_result = {'red_balls': actual_red, 'blue_balls': actual_blue}
                
                # 更新性能反馈
                ensemble.update_performance_feedback(prediction, actual_result)
                
                # 计算杀号成功率
                red_kills = prediction.get('red_kills', [])
                blue_kills = prediction.get('blue_kills', [])
                
                red_success = all(num not in actual_red for num in red_kills)
                blue_success = all(num not in actual_blue for num in blue_kills)
                overall_success = red_success and blue_success
                
                test_results.append({
                    'period': period,
                    'prediction': prediction,
                    'actual': actual_result,
                    'red_success': red_success,
                    'blue_success': blue_success,
                    'overall_success': overall_success
                })
                
                print(f"   期号 {period}: 杀号成功={overall_success} (红球={red_success}, 蓝球={blue_success})")
                
            except Exception as e:
                print(f"   期号 {period} 测试失败: {e}")
                continue
        
        # 计算测试结果
        if test_results:
            success_count = sum(1 for r in test_results if r['overall_success'])
            success_rate = success_count / len(test_results)
            
            print(f"\n📊 集成预测器测试结果:")
            print(f"   测试期数: {len(test_results)}")
            print(f"   成功期数: {success_count}")
            print(f"   成功率: {success_rate:.4f}")
            
            # 获取性能指标
            metrics = ensemble.get_accuracy_metrics()
            if metrics:
                print(f"   整体杀号成功率: {metrics.get('overall_kill_success_rate', 0):.4f}")
                print(f"   红球杀号成功率: {metrics.get('red_kill_success_rate', 0):.4f}")
                print(f"   蓝球杀号成功率: {metrics.get('blue_kill_success_rate', 0):.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 集成预测器测试失败: {e}")
        return False

def test_accuracy_optimizer():
    """测试准确率优化器"""
    print("\n🎯 测试准确率优化器...")
    
    try:
        from src.optimization.accuracy_enhancer import AccuracyOptimizer, AccuracyConfig
        
        # 创建配置
        config = AccuracyConfig(
            target_kill_success_rate=0.95,
            ensemble_size=3,
            fusion_method='adaptive',
            online_learning=True
        )
        
        # 创建优化器
        optimizer = AccuracyOptimizer(config)
        
        # 加载数据
        data = load_lottery_data()
        
        print("🚀 执行准确率优化...")
        
        # 执行优化（使用较少期数进行快速测试）
        optimization_result = optimizer.optimize_accuracy(data, backtest_periods=10)
        
        # 显示优化结果
        summary = optimization_result.get('summary', {})
        
        print(f"\n📊 准确率优化结果:")
        print(f"   总预测期数: {summary.get('total_predictions', 0)}")
        print(f"   杀号成功期数: {summary.get('kill_success_count', 0)}")
        print(f"   杀号成功率: {summary.get('kill_success_rate', 0):.4f}")
        print(f"   红球杀号成功率: {summary.get('red_kill_success_rate', 0):.4f}")
        print(f"   蓝球杀号成功率: {summary.get('blue_kill_success_rate', 0):.4f}")
        print(f"   目标达成: {'✅' if summary.get('target_achieved', False) else '❌'}")
        
        # 保存优化结果
        results_dir = Path("optimization_results")
        results_dir.mkdir(exist_ok=True)
        
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        result_file = results_dir / f"accuracy_optimization_{timestamp}.json"
        
        with open(result_file, 'w', encoding='utf-8') as f:
            json.dump(optimization_result, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"📁 优化结果已保存: {result_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 准确率优化器测试失败: {e}")
        return False

def run_full_accuracy_enhancement():
    """运行完整的准确率提升流程"""
    print("\n🚀 运行完整准确率提升流程...")
    
    try:
        from src.optimization.accuracy_enhancer import AccuracyOptimizer, AccuracyConfig
        
        # 创建高性能配置
        config = AccuracyConfig(
            target_kill_success_rate=0.95,
            target_prediction_accuracy=0.90,
            ensemble_size=5,
            fusion_method='adaptive',
            online_learning=True,
            learning_rate=0.01,
            performance_window=30,
            diversity_penalty=0.1
        )
        
        print(f"⚙️ 配置参数:")
        print(f"   目标杀号成功率: {config.target_kill_success_rate}")
        print(f"   集成模型数量: {config.ensemble_size}")
        print(f"   融合方法: {config.fusion_method}")
        print(f"   在线学习: {config.online_learning}")
        
        # 创建优化器
        optimizer = AccuracyOptimizer(config)
        
        # 加载数据
        data = load_lottery_data()
        print(f"📊 数据量: {len(data)} 期")
        
        # 执行完整优化
        print("🎯 开始完整准确率优化...")
        start_time = time.time()
        
        # 使用更多期数进行完整测试
        backtest_periods = min(50, len(data) // 2)
        optimization_result = optimizer.optimize_accuracy(data, backtest_periods=backtest_periods)
        
        end_time = time.time()
        optimization_time = end_time - start_time
        
        # 分析结果
        summary = optimization_result.get('summary', {})
        
        print(f"\n🎉 完整准确率提升完成!")
        print(f"⏱️ 优化时间: {optimization_time:.1f} 秒")
        print(f"📊 优化结果:")
        print(f"   回测期数: {backtest_periods}")
        print(f"   总预测期数: {summary.get('total_predictions', 0)}")
        print(f"   杀号成功期数: {summary.get('kill_success_count', 0)}")
        print(f"   杀号成功率: {summary.get('kill_success_rate', 0):.4f}")
        print(f"   红球杀号成功率: {summary.get('red_kill_success_rate', 0):.4f}")
        print(f"   蓝球杀号成功率: {summary.get('blue_kill_success_rate', 0):.4f}")
        
        # 检查目标达成情况
        target_achieved = summary.get('target_achieved', False)
        current_rate = summary.get('kill_success_rate', 0)
        
        if target_achieved:
            print(f"🎯 目标达成! 杀号成功率 {current_rate:.4f} >= 目标 {config.target_kill_success_rate}")
        else:
            improvement_needed = config.target_kill_success_rate - current_rate
            print(f"⚠️ 目标未达成，还需提升 {improvement_needed:.4f} ({improvement_needed*100:.1f}%)")
        
        # 保存详细结果
        results_dir = Path("optimization_results")
        results_dir.mkdir(exist_ok=True)
        
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        
        # 保存完整结果
        full_result_file = results_dir / f"full_accuracy_enhancement_{timestamp}.json"
        with open(full_result_file, 'w', encoding='utf-8') as f:
            json.dump(optimization_result, f, ensure_ascii=False, indent=2, default=str)
        
        # 保存性能数据
        performance_file = results_dir / f"performance_data_{timestamp}.json"
        optimizer.ensemble_predictor.save_performance_data(str(performance_file))
        
        print(f"📁 完整结果已保存: {full_result_file}")
        print(f"📈 性能数据已保存: {performance_file}")
        
        # 生成提升建议
        generate_improvement_suggestions(summary, config)
        
        return True
        
    except Exception as e:
        print(f"❌ 完整准确率提升失败: {e}")
        return False

def generate_improvement_suggestions(summary: Dict[str, Any], config):
    """生成改进建议"""
    print(f"\n💡 改进建议:")
    
    current_rate = summary.get('kill_success_rate', 0)
    target_rate = config.target_kill_success_rate
    
    if current_rate < target_rate:
        gap = target_rate - current_rate
        
        if gap > 0.2:
            print("   🔧 建议大幅调整:")
            print("     - 增加更多高质量预测器")
            print("     - 优化集成融合算法")
            print("     - 调整在线学习参数")
        elif gap > 0.1:
            print("   ⚙️ 建议中等调整:")
            print("     - 微调模型权重")
            print("     - 优化多样性参数")
            print("     - 增加性能窗口大小")
        else:
            print("   🎯 建议微调:")
            print("     - 调整学习率")
            print("     - 优化置信度阈值")
            print("     - 增加回测期数")
    
    # 针对红球和蓝球的具体建议
    red_rate = summary.get('red_kill_success_rate', 0)
    blue_rate = summary.get('blue_kill_success_rate', 0)
    
    if red_rate < 0.9:
        print("   🔴 红球杀号改进:")
        print("     - 增强红球预测算法")
        print("     - 调整红球杀号数量")
    
    if blue_rate < 0.9:
        print("   🔵 蓝球杀号改进:")
        print("     - 优化蓝球预测模型")
        print("     - 调整蓝球权重分配")

def main():
    """主函数"""
    print("🎯 预测准确率提升系统")
    print("=" * 60)
    
    # 创建日志目录
    Path("logs").mkdir(exist_ok=True)
    
    tests = [
        ("集成预测器测试", test_ensemble_predictor),
        ("准确率优化器测试", test_accuracy_optimizer),
        ("完整准确率提升", run_full_accuracy_enhancement),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append(result)
            if result:
                print(f"✅ {test_name} 成功")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"💥 {test_name} 异常: {e}")
            results.append(False)
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 预测准确率提升结果:")
    passed = sum(results)
    total = len(results)
    print(f"✅ 通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！预测准确率提升系统运行正常！")
        return 0
    elif passed >= total * 0.7:
        print("⚠️ 大部分测试通过，系统基本可用")
        return 0
    else:
        print("❌ 多个测试失败，需要修复")
        return 1

if __name__ == "__main__":
    exit(main())
