#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的号码生成器 - 专注解决2+1命中率为0的问题
"""

import pandas as pd
import numpy as np
import random
from typing import List, Tuple

class ImprovedNumberGenerator:
    """改进的号码生成器 - 确保有实际命中"""
    
    def __init__(self):
        self.hot_cold_balance = 0.6  # 热号比例
        self.position_weights = [1.0, 1.1, 1.2, 1.1, 1.0]  # 位置权重
        
    def generate_red_balls(self, data: pd.DataFrame, kill_numbers: List[int], 
                          target_odd_even: str, target_size: str) -> List[int]:
        """生成红球 - 多策略确保命中"""
        available_numbers = [i for i in range(1, 36) if i not in kill_numbers]
        
        if len(available_numbers) < 5:
            available_numbers = list(range(1, 36))
        
        # 策略1: 热号选择 (40%)
        hot_numbers = self._get_hot_numbers(data, available_numbers, 15)
        
        # 策略2: 位置分析 (30%)
        position_numbers = self._get_position_preferred(data, available_numbers, 12)
        
        # 策略3: 随机选择 (30%) - 确保多样性
        random_numbers = random.sample(available_numbers, min(10, len(available_numbers)))
        
        # 综合选择
        candidate_pool = list(set(hot_numbers + position_numbers + random_numbers))
        
        # 确保满足比例要求
        selected = self._select_with_ratio_constraint(
            candidate_pool, target_odd_even, target_size
        )
        
        return sorted(selected[:5])
    
    def generate_blue_balls(self, data: pd.DataFrame, kill_numbers: List[int],
                           target_ratio: str) -> List[int]:
        """生成蓝球"""
        available_numbers = [i for i in range(1, 13) if i not in kill_numbers]
        
        if len(available_numbers) < 2:
            available_numbers = list(range(1, 13))
        
        # 简单的热号策略
        recent_data = data.tail(15)
        blue_freq = {}
        
        for _, row in recent_data.iterrows():
            for i in range(1, 3):
                num = row[f'蓝球{i}']
                if num in available_numbers:
                    blue_freq[num] = blue_freq.get(num, 0) + 1
        
        # 选择中等频率的号码 (避免过热或过冷)
        if blue_freq:
            sorted_blues = sorted(blue_freq.items(), key=lambda x: x[1])
            mid_start = len(sorted_blues) // 4
            mid_end = 3 * len(sorted_blues) // 4
            candidates = [num for num, _ in sorted_blues[mid_start:mid_end]]
        else:
            candidates = available_numbers
        
        # 随机选择2个
        if len(candidates) >= 2:
            selected = random.sample(candidates, 2)
        else:
            selected = random.sample(available_numbers, min(2, len(available_numbers)))
        
        return sorted(selected)
    
    def _get_hot_numbers(self, data: pd.DataFrame, available: List[int], count: int) -> List[int]:
        """获取热号"""
        recent_data = data.tail(20)
        number_freq = {}
        
        for _, row in recent_data.iterrows():
            for i in range(1, 6):
                num = row[f'红球{i}']
                if num in available:
                    number_freq[num] = number_freq.get(num, 0) + 1
        
        sorted_numbers = sorted(number_freq.items(), key=lambda x: x[1], reverse=True)
        return [num for num, _ in sorted_numbers[:count]]
    
    def _get_position_preferred(self, data: pd.DataFrame, available: List[int], count: int) -> List[int]:
        """基于位置分析的首选号码"""
        recent_data = data.tail(15)
        position_scores = {}
        
        for num in available:
            score = 0
            for _, row in recent_data.iterrows():
                for pos in range(5):
                    if row[f'红球{pos+1}'] == num:
                        score += self.position_weights[pos]
            position_scores[num] = score
        
        sorted_numbers = sorted(position_scores.items(), key=lambda x: x[1], reverse=True)
        return [num for num, _ in sorted_numbers[:count]]
    
    def _select_with_ratio_constraint(self, candidates: List[int], 
                                    target_odd_even: str, target_size: str) -> List[int]:
        """在比例约束下选择号码"""
        if len(candidates) < 5:
            return random.sample(candidates, min(5, len(candidates)))
        
        # 解析目标比例
        try:
            odd_target = int(target_odd_even.split(':')[0])
            big_target = int(target_size.split(':')[0])
        except:
            odd_target = 3
            big_target = 2
        
        # 分类候选号码
        odd_candidates = [n for n in candidates if n % 2 == 1]
        even_candidates = [n for n in candidates if n % 2 == 0]
        big_candidates = [n for n in candidates if n > 18]
        small_candidates = [n for n in candidates if n <= 18]
        
        selected = []
        
        # 尽量满足奇偶比
        odd_needed = min(odd_target, len(odd_candidates))
        even_needed = min(5 - odd_needed, len(even_candidates))
        
        if odd_needed > 0:
            selected.extend(random.sample(odd_candidates, odd_needed))
        if even_needed > 0:
            remaining_even = [n for n in even_candidates if n not in selected]
            selected.extend(random.sample(remaining_even, min(even_needed, len(remaining_even))))
        
        # 补充到5个
        while len(selected) < 5:
            remaining = [n for n in candidates if n not in selected]
            if remaining:
                selected.append(random.choice(remaining))
            else:
                break
        
        return selected[:5]
