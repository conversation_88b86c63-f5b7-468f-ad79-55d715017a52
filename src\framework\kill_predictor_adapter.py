"""
杀号预测器适配器
专门用于杀号参数优化的预测器适配器
"""

import pandas as pd
from typing import Dict, List, Any, Tuple
from datetime import datetime

from .interfaces import PredictorInterface
from .data_models import PredictionResult
from ..apps.advanced_probabilistic_system import AdvancedProbabilisticSystem
from ..utils.utils import parse_numbers


class KillPredictorAdapter(PredictorInterface):
    """
    杀号预测器适配器
    
    将AdvancedProbabilisticSystem的杀号功能包装成统一框架接口
    """
    
    def __init__(self, 
                 bayesian_weight: float = 0.5,
                 markov_weight: float = 0.5,
                 red_kill_count: int = 6,
                 blue_kill_count: int = 2):
        """
        初始化杀号预测器适配器
        
        Args:
            bayesian_weight: 贝叶斯权重
            markov_weight: 马尔可夫权重
            red_kill_count: 红球杀号数量
            blue_kill_count: 蓝球杀号数量
        """
        self.bayesian_weight = bayesian_weight
        self.markov_weight = markov_weight
        self.red_kill_count = red_kill_count
        self.blue_kill_count = blue_kill_count
        
        # 验证权重
        if abs(bayesian_weight + markov_weight - 1.0) > 0.01:
            raise ValueError("贝叶斯权重和马尔可夫权重之和必须等于1.0")
        
        self.system = None
        self.data = None
        
    def _initialize_system(self, data: pd.DataFrame):
        """初始化高级概率系统"""
        if self.system is None or not self.data.equals(data):
            self.system = AdvancedProbabilisticSystem()
            self.system.data = data
            self.system.initialize_system()
            self.system.set_kill_weights(self.bayesian_weight, self.markov_weight)
            self.data = data.copy()
    
    def predict_for_period(self, data_index: int, data: pd.DataFrame) -> PredictionResult:
        """
        为指定数据索引预测杀号
        
        Args:
            data_index: 当前数据索引（用于训练的最新数据）
            data: 完整的历史数据
            
        Returns:
            PredictionResult: 标准化的预测结果
        """
        # 初始化系统
        training_data = data.iloc[:data_index + 1].copy()
        self._initialize_system(training_data)
        
        # 获取当前期号
        current_period = str(data.iloc[data_index]['期号'])
        
        # 构建期数据
        period_data = self._build_period_data(data_index, data)
        
        # 预测杀号
        try:
            red_kills = self.system.predict_red_kills(period_data, self.red_kill_count)
            blue_kills = self.system.predict_blue_kills(period_data, self.blue_kill_count)
        except Exception as e:
            print(f"⚠️ 杀号预测失败: {e}")
            red_kills = []
            blue_kills = []
        
        # 构建杀号信息（使用BacktestFramework期望的键名）
        kill_numbers = {
            'red_universal': red_kills,      # BacktestFramework期望的键名
            'blue_universal': blue_kills,    # BacktestFramework期望的键名
            'red_kills': red_kills,          # 保留原键名以兼容
            'blue_kills': blue_kills,        # 保留原键名以兼容
            'red_kill_count': len(red_kills),
            'blue_kill_count': len(blue_kills),
            'bayesian_weight': self.bayesian_weight,
            'markov_weight': self.markov_weight
        }
        
        # 返回标准化结果
        return PredictionResult(
            period_number=current_period,
            data_index=data_index,
            red_odd_even_predictions=[],  # 杀号预测器不关注比例预测
            red_size_predictions=[],
            blue_size_predictions=[],
            generated_numbers=([], []),   # 杀号预测器不生成号码
            kill_numbers=kill_numbers,
            predictor_name=self.get_predictor_name(),
            prediction_time=datetime.now(),
            training_data_size=len(training_data)
        )
    
    def _build_period_data(self, current_index: int, data: pd.DataFrame) -> Dict:
        """构建期数据"""
        period_data = {}
        
        # 当前期
        if current_index < len(data):
            period_data['current'] = data.iloc[current_index]
        
        # 上期
        if current_index + 1 < len(data):
            period_data['prev'] = data.iloc[current_index + 1]
        
        # 上上期
        if current_index + 2 < len(data):
            period_data['prev2'] = data.iloc[current_index + 2]
        
        return period_data
    
    def get_predictor_name(self) -> str:
        """获取预测器名称"""
        return f"KillPredictor_B{self.bayesian_weight:.1f}_M{self.markov_weight:.1f}_R{self.red_kill_count}_B{self.blue_kill_count}"
    
    def get_predictor_version(self) -> str:
        """获取预测器版本"""
        return "1.0"
    
    def get_parameter_info(self) -> Dict[str, Any]:
        """获取参数信息"""
        return {
            'bayesian_weight': self.bayesian_weight,
            'markov_weight': self.markov_weight,
            'red_kill_count': self.red_kill_count,
            'blue_kill_count': self.blue_kill_count
        }
