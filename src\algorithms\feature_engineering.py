"""
机器学习特征工程模块
用于彩票比值预测的特征提取和处理
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any
from sklearn.preprocessing import StandardScaler, LabelEncoder
import warnings
warnings.filterwarnings('ignore')

class LotteryFeatureExtractor:
    """彩票特征提取器"""
    
    def __init__(self, lookback_periods: int = 20):
        """
        初始化特征提取器
        
        Args:
            lookback_periods: 回看期数，用于构建历史特征
        """
        self.lookback_periods = lookback_periods
        self.scaler = StandardScaler()
        self.label_encoders = {}
        
    def extract_features(self, data: pd.DataFrame, target_index: int) -> Dict[str, Any]:
        """
        提取指定期数的特征
        
        Args:
            data: 历史数据
            target_index: 目标期数索引（要预测的期数）
            
        Returns:
            特征字典
        """
        if target_index < self.lookback_periods:
            # 如果历史数据不足，使用所有可用数据
            start_index = 0
        else:
            start_index = target_index - self.lookback_periods
            
        # 获取历史数据段
        history_data = data.iloc[start_index:target_index]
        
        features = {}
        
        # 1. 基础比值特征
        features.update(self._extract_ratio_features(history_data))
        
        # 2. 趋势特征
        features.update(self._extract_trend_features(history_data))
        
        # 3. 周期性特征
        features.update(self._extract_cyclical_features(history_data, target_index))
        
        # 4. 统计特征
        features.update(self._extract_statistical_features(history_data))
        
        # 5. 序列模式特征
        features.update(self._extract_sequence_features(history_data))
        
        return features
    
    def _extract_ratio_features(self, data: pd.DataFrame) -> Dict[str, float]:
        """提取比值相关特征"""
        features = {}
        
        if len(data) == 0:
            return features
            
        # 计算历史比值
        red_odd_even_ratios = []
        red_size_ratios = []
        blue_size_ratios = []
        
        for _, row in data.iterrows():
            # 解析号码
            red_nums = [int(x) for x in str(row['红球']).split(',')]
            blue_nums = [int(x) for x in str(row['蓝球']).split(',')]
            
            # 计算比值
            red_odd_count = sum(1 for x in red_nums if x % 2 == 1)
            red_even_count = len(red_nums) - red_odd_count
            red_odd_even_ratios.append(f"{red_odd_count}:{red_even_count}")
            
            red_big_count = sum(1 for x in red_nums if x > 17)
            red_small_count = len(red_nums) - red_big_count
            red_size_ratios.append(f"{red_big_count}:{red_small_count}")
            
            blue_big_count = sum(1 for x in blue_nums if x > 6)
            blue_small_count = len(blue_nums) - blue_big_count
            blue_size_ratios.append(f"{blue_big_count}:{blue_small_count}")
        
        # 最近N期比值特征
        for i in [1, 3, 5, 10]:
            if len(red_odd_even_ratios) >= i:
                features[f'red_odd_even_last_{i}'] = red_odd_even_ratios[-i]
                features[f'red_size_last_{i}'] = red_size_ratios[-i]
                features[f'blue_size_last_{i}'] = blue_size_ratios[-i]
        
        # 比值频率统计
        if red_odd_even_ratios:
            from collections import Counter
            red_odd_even_counter = Counter(red_odd_even_ratios)
            red_size_counter = Counter(red_size_ratios)
            blue_size_counter = Counter(blue_size_ratios)
            
            # 最常见的比值
            features['red_odd_even_most_common'] = red_odd_even_counter.most_common(1)[0][0]
            features['red_size_most_common'] = red_size_counter.most_common(1)[0][0]
            features['blue_size_most_common'] = blue_size_counter.most_common(1)[0][0]
            
            # 比值多样性（熵）
            features['red_odd_even_entropy'] = self._calculate_entropy(red_odd_even_counter)
            features['red_size_entropy'] = self._calculate_entropy(red_size_counter)
            features['blue_size_entropy'] = self._calculate_entropy(blue_size_counter)
        
        return features
    
    def _extract_trend_features(self, data: pd.DataFrame) -> Dict[str, float]:
        """提取趋势特征"""
        features = {}
        
        if len(data) < 3:
            return features
            
        # 计算数值化的比值序列
        red_odd_ratios = []
        red_size_ratios = []
        blue_size_ratios = []
        
        for _, row in data.iterrows():
            red_nums = [int(x) for x in str(row['红球']).split(',')]
            blue_nums = [int(x) for x in str(row['蓝球']).split(',')]
            
            red_odd_count = sum(1 for x in red_nums if x % 2 == 1)
            red_big_count = sum(1 for x in red_nums if x > 17)
            blue_big_count = sum(1 for x in blue_nums if x > 6)
            
            red_odd_ratios.append(red_odd_count / len(red_nums))
            red_size_ratios.append(red_big_count / len(red_nums))
            blue_size_ratios.append(blue_big_count / len(blue_nums))
        
        # 趋势方向
        features['red_odd_trend'] = self._calculate_trend(red_odd_ratios)
        features['red_size_trend'] = self._calculate_trend(red_size_ratios)
        features['blue_size_trend'] = self._calculate_trend(blue_size_ratios)
        
        # 趋势强度
        features['red_odd_trend_strength'] = self._calculate_trend_strength(red_odd_ratios)
        features['red_size_trend_strength'] = self._calculate_trend_strength(red_size_ratios)
        features['blue_size_trend_strength'] = self._calculate_trend_strength(blue_size_ratios)
        
        return features
    
    def _extract_cyclical_features(self, data: pd.DataFrame, target_index: int) -> Dict[str, float]:
        """提取周期性特征"""
        features = {}
        
        # 周期位置特征
        features['cycle_position_7'] = target_index % 7  # 周周期
        features['cycle_position_10'] = target_index % 10  # 十期周期
        features['cycle_position_30'] = target_index % 30  # 月周期
        
        # 正弦余弦编码周期特征
        features['cycle_sin_7'] = np.sin(2 * np.pi * (target_index % 7) / 7)
        features['cycle_cos_7'] = np.cos(2 * np.pi * (target_index % 7) / 7)
        features['cycle_sin_10'] = np.sin(2 * np.pi * (target_index % 10) / 10)
        features['cycle_cos_10'] = np.cos(2 * np.pi * (target_index % 10) / 10)
        
        return features
    
    def _extract_statistical_features(self, data: pd.DataFrame) -> Dict[str, float]:
        """提取统计特征"""
        features = {}
        
        if len(data) < 2:
            return features
            
        # 计算数值化序列
        red_odd_ratios = []
        red_size_ratios = []
        blue_size_ratios = []
        
        for _, row in data.iterrows():
            red_nums = [int(x) for x in str(row['红球']).split(',')]
            blue_nums = [int(x) for x in str(row['蓝球']).split(',')]
            
            red_odd_count = sum(1 for x in red_nums if x % 2 == 1)
            red_big_count = sum(1 for x in red_nums if x > 17)
            blue_big_count = sum(1 for x in blue_nums if x > 6)
            
            red_odd_ratios.append(red_odd_count)
            red_size_ratios.append(red_big_count)
            blue_size_ratios.append(blue_big_count)
        
        # 统计特征
        for name, values in [
            ('red_odd', red_odd_ratios),
            ('red_size', red_size_ratios),
            ('blue_size', blue_size_ratios)
        ]:
            if values:
                features[f'{name}_mean'] = np.mean(values)
                features[f'{name}_std'] = np.std(values)
                features[f'{name}_min'] = np.min(values)
                features[f'{name}_max'] = np.max(values)
                features[f'{name}_median'] = np.median(values)
                
                # 移动平均
                if len(values) >= 3:
                    features[f'{name}_ma3'] = np.mean(values[-3:])
                if len(values) >= 5:
                    features[f'{name}_ma5'] = np.mean(values[-5:])
        
        return features
