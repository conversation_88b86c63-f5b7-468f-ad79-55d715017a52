#!/usr/bin/env python3
"""
预测失败分析脚本
Prediction Failure Analysis Script

详细分析每个期数的预测失败原因，为算法优化提供数据支持
"""

import sys
from pathlib import Path
import pandas as pd
import numpy as np
import time
import logging
from typing import Dict, List, Tuple, Any
from collections import Counter, defaultdict
import json

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/failure_analysis.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class DetailedFailureAnalyzer:
    """详细失败分析器"""
    
    def __init__(self):
        self.logger = logging.getLogger(f"failure_analysis.{self.__class__.__name__}")
        
    def load_data(self) -> pd.DataFrame:
        """加载数据"""
        data_path = Path("data/raw/dlt_data.csv")
        if data_path.exists():
            data = pd.read_csv(data_path)
            self.logger.info(f"加载数据: {len(data)} 条记录")
            return data
        else:
            self.logger.error("数据文件不存在")
            return pd.DataFrame()
    
    def parse_numbers(self, row: pd.Series) -> Tuple[List[int], List[int]]:
        """解析红球和蓝球数字"""
        red_numbers = []
        for i in range(1, 6):  # 红球1到红球5
            col_name = f'红球{i}'
            if col_name in row and pd.notna(row[col_name]):
                red_numbers.append(int(row[col_name]))
        
        blue_numbers = []
        for i in range(1, 3):  # 蓝球1到蓝球2
            col_name = f'蓝球{i}'
            if col_name in row and pd.notna(row[col_name]):
                blue_numbers.append(int(row[col_name]))
        
        return red_numbers, blue_numbers
    
    def calculate_ratios(self, red_numbers: List[int], blue_numbers: List[int]) -> Dict[str, str]:
        """计算各种比例"""
        ratios = {}
        
        if len(red_numbers) >= 5:
            # 红球奇偶比
            odd_count = sum(1 for num in red_numbers if num % 2 == 1)
            even_count = 5 - odd_count
            ratios['red_odd_even'] = f"{odd_count}:{even_count}"
            
            # 红球大小比 (大数:小数格式，1-18小号，19-35大号)
            big_count = sum(1 for num in red_numbers if num >= 19)
            small_count = 5 - big_count
            ratios['red_size'] = f"{big_count}:{small_count}"
        
        if len(blue_numbers) >= 2:
            # 蓝球大小比 (大数:小数格式，1-6小号，7-12大号)
            big_count = sum(1 for num in blue_numbers if num >= 7)
            small_count = 2 - big_count
            ratios['blue_size'] = f"{big_count}:{small_count}"
        
        return ratios
    
    def simple_pattern_prediction(self, historical_patterns: List[str], window_size: int = 20) -> str:
        """简单模式预测"""
        if len(historical_patterns) < window_size:
            window_size = len(historical_patterns)
        
        recent_patterns = historical_patterns[-window_size:]
        pattern_counts = Counter(recent_patterns)
        
        if pattern_counts:
            # 返回最频繁的模式
            return pattern_counts.most_common(1)[0][0]
        
        return "2:3"  # 默认值
    
    def analyze_period_failures(self, data: pd.DataFrame, test_periods: int = 10) -> Dict[str, Any]:
        """分析期数失败详情"""
        if len(data) < test_periods + 50:
            self.logger.error("数据不足进行分析")
            return {}
        
        failure_analysis = {
            'period_details': [],
            'pattern_analysis': {
                'red_odd_even': {'success': [], 'failure': []},
                'red_size': {'success': [], 'failure': []},
                'blue_size': {'success': [], 'failure': []}
            },
            'kill_analysis': {
                'red_kill_success': [],
                'red_kill_failure': [],
                'blue_kill_success': [],
                'blue_kill_failure': []
            }
        }
        
        # 收集历史模式
        historical_data = data.iloc[:-test_periods]
        red_odd_even_patterns = []
        red_size_patterns = []
        blue_size_patterns = []
        
        for _, row in historical_data.iterrows():
            red_numbers, blue_numbers = self.parse_numbers(row)
            ratios = self.calculate_ratios(red_numbers, blue_numbers)
            
            if 'red_odd_even' in ratios:
                red_odd_even_patterns.append(ratios['red_odd_even'])
            if 'red_size' in ratios:
                red_size_patterns.append(ratios['red_size'])
            if 'blue_size' in ratios:
                blue_size_patterns.append(ratios['blue_size'])
        
        # 分析每个测试期数
        for i in range(test_periods):
            test_index = len(data) - test_periods + i
            train_data = data.iloc[:test_index]
            actual_data = data.iloc[test_index]
            
            period_num = actual_data['期号']
            
            # 解析实际数据
            actual_red, actual_blue = self.parse_numbers(actual_data)
            actual_ratios = self.calculate_ratios(actual_red, actual_blue)
            
            if len(actual_red) < 5 or len(actual_blue) < 2:
                continue
            
            # 预测比例
            pred_red_odd_even = self.simple_pattern_prediction(red_odd_even_patterns[-100:])
            pred_red_size = self.simple_pattern_prediction(red_size_patterns[-100:])
            pred_blue_size = self.simple_pattern_prediction(blue_size_patterns[-100:])
            
            # 简单杀号策略
            red_kills = self.simple_kill_strategy(train_data, 'red')
            blue_kills = self.simple_kill_strategy(train_data, 'blue')
            
            # 检查预测结果
            red_odd_even_hit = pred_red_odd_even == actual_ratios.get('red_odd_even', '')
            red_size_hit = pred_red_size == actual_ratios.get('red_size', '')
            blue_size_hit = pred_blue_size == actual_ratios.get('blue_size', '')
            
            red_kill_hit = all(num not in actual_red for num in red_kills)
            blue_kill_hit = all(num not in actual_blue for num in blue_kills)
            
            # 记录详细信息
            period_detail = {
                'period': period_num,
                'actual_red': actual_red,
                'actual_blue': actual_blue,
                'actual_ratios': actual_ratios,
                'predictions': {
                    'red_odd_even': pred_red_odd_even,
                    'red_size': pred_red_size,
                    'blue_size': pred_blue_size
                },
                'kills': {
                    'red_kills': red_kills,
                    'blue_kills': blue_kills
                },
                'results': {
                    'red_odd_even_hit': red_odd_even_hit,
                    'red_size_hit': red_size_hit,
                    'blue_size_hit': blue_size_hit,
                    'red_kill_hit': red_kill_hit,
                    'blue_kill_hit': blue_kill_hit
                }
            }
            
            failure_analysis['period_details'].append(period_detail)
            
            # 分类成功和失败案例
            if red_odd_even_hit:
                failure_analysis['pattern_analysis']['red_odd_even']['success'].append(period_detail)
            else:
                failure_analysis['pattern_analysis']['red_odd_even']['failure'].append(period_detail)
            
            if red_size_hit:
                failure_analysis['pattern_analysis']['red_size']['success'].append(period_detail)
            else:
                failure_analysis['pattern_analysis']['red_size']['failure'].append(period_detail)
            
            if blue_size_hit:
                failure_analysis['pattern_analysis']['blue_size']['success'].append(period_detail)
            else:
                failure_analysis['pattern_analysis']['blue_size']['failure'].append(period_detail)
            
            if red_kill_hit:
                failure_analysis['kill_analysis']['red_kill_success'].append(period_detail)
            else:
                failure_analysis['kill_analysis']['red_kill_failure'].append(period_detail)
            
            if blue_kill_hit:
                failure_analysis['kill_analysis']['blue_kill_success'].append(period_detail)
            else:
                failure_analysis['kill_analysis']['blue_kill_failure'].append(period_detail)
        
        return failure_analysis
    
    def simple_kill_strategy(self, data: pd.DataFrame, ball_type: str) -> List[int]:
        """简单杀号策略"""
        if ball_type == 'red':
            max_num = 35
            kill_count = 5
        else:
            max_num = 12
            kill_count = 2
        
        frequency = defaultdict(int)
        recent_data = data.tail(30)  # 最近30期
        
        for _, row in recent_data.iterrows():
            numbers = []
            if ball_type == 'red':
                for i in range(1, 6):
                    col_name = f'红球{i}'
                    if col_name in row and pd.notna(row[col_name]):
                        numbers.append(int(row[col_name]))
            else:
                for i in range(1, 3):
                    col_name = f'蓝球{i}'
                    if col_name in row and pd.notna(row[col_name]):
                        numbers.append(int(row[col_name]))
            
            for num in numbers:
                if 1 <= num <= max_num:
                    frequency[num] += 1
        
        # 杀掉出现频率最低的数字
        sorted_freq = sorted(frequency.items(), key=lambda x: x[1])
        return [num for num, _ in sorted_freq[:kill_count]]
    
    def generate_failure_report(self, failure_analysis: Dict[str, Any]) -> str:
        """生成失败分析报告"""
        report = []
        report.append("=" * 80)
        report.append("预测失败详细分析报告")
        report.append("=" * 80)
        
        # 总体统计
        total_periods = len(failure_analysis['period_details'])
        
        red_odd_even_success = len(failure_analysis['pattern_analysis']['red_odd_even']['success'])
        red_size_success = len(failure_analysis['pattern_analysis']['red_size']['success'])
        blue_size_success = len(failure_analysis['pattern_analysis']['blue_size']['success'])
        red_kill_success = len(failure_analysis['kill_analysis']['red_kill_success'])
        blue_kill_success = len(failure_analysis['kill_analysis']['blue_kill_success'])
        
        report.append(f"\n📊 总体统计 (共{total_periods}期):")
        report.append(f"   红球奇偶比成功: {red_odd_even_success}/{total_periods} ({red_odd_even_success/total_periods:.1%})")
        report.append(f"   红球大小比成功: {red_size_success}/{total_periods} ({red_size_success/total_periods:.1%})")
        report.append(f"   蓝球大小比成功: {blue_size_success}/{total_periods} ({blue_size_success/total_periods:.1%})")
        report.append(f"   红球杀号成功: {red_kill_success}/{total_periods} ({red_kill_success/total_periods:.1%})")
        report.append(f"   蓝球杀号成功: {blue_kill_success}/{total_periods} ({blue_kill_success/total_periods:.1%})")
        
        # 详细期数分析
        report.append(f"\n📋 详细期数分析:")
        for detail in failure_analysis['period_details']:
            period = detail['period']
            results = detail['results']
            
            report.append(f"\n期号 {period}:")
            report.append(f"   实际红球: {detail['actual_red']}")
            report.append(f"   实际蓝球: {detail['actual_blue']}")
            report.append(f"   实际比例: {detail['actual_ratios']}")
            report.append(f"   预测比例: {detail['predictions']}")
            report.append(f"   杀号: 红球{detail['kills']['red_kills']} 蓝球{detail['kills']['blue_kills']}")
            
            status_symbols = {
                'red_odd_even_hit': '✅' if results['red_odd_even_hit'] else '❌',
                'red_size_hit': '✅' if results['red_size_hit'] else '❌',
                'blue_size_hit': '✅' if results['blue_size_hit'] else '❌',
                'red_kill_hit': '✅' if results['red_kill_hit'] else '❌',
                'blue_kill_hit': '✅' if results['blue_kill_hit'] else '❌'
            }
            
            report.append(f"   结果: 红奇偶{status_symbols['red_odd_even_hit']} 红大小{status_symbols['red_size_hit']} 蓝大小{status_symbols['blue_size_hit']} 红杀{status_symbols['red_kill_hit']} 蓝杀{status_symbols['blue_kill_hit']}")
        
        return "\n".join(report)


def main():
    """主函数"""
    print("🔍 预测失败详细分析系统")
    print("=" * 60)
    
    # 创建日志目录
    Path("logs").mkdir(exist_ok=True)
    
    analyzer = DetailedFailureAnalyzer()
    
    # 加载数据
    print("📊 加载数据...")
    data = analyzer.load_data()
    
    if data.empty:
        print("❌ 数据加载失败")
        return 1
    
    # 分析失败原因
    print("🔍 分析预测失败原因...")
    start_time = time.time()
    
    failure_analysis = analyzer.analyze_period_failures(data, test_periods=10)
    
    end_time = time.time()
    
    if failure_analysis:
        print(f"✅ 分析完成，耗时 {end_time - start_time:.2f} 秒")
        
        # 生成报告
        report = analyzer.generate_failure_report(failure_analysis)
        print(report)
        
        # 保存详细分析结果
        output_dir = Path("analysis_results")
        output_dir.mkdir(exist_ok=True)
        
        # 保存JSON格式的详细数据
        with open(output_dir / "failure_analysis.json", 'w', encoding='utf-8') as f:
            json.dump(failure_analysis, f, ensure_ascii=False, indent=2, default=str)
        
        # 保存文本报告
        with open(output_dir / "failure_report.txt", 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"\n💾 详细分析结果已保存到 {output_dir}")
        print("   - failure_analysis.json: 完整分析数据")
        print("   - failure_report.txt: 文本格式报告")
    
    return 0

if __name__ == "__main__":
    exit(main())
