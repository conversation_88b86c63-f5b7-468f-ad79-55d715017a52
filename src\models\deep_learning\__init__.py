"""
深度学习模型模块

提供现代化的深度学习预测器，包括：
- LSTM时间序列预测器
- Transformer预测器  
- 多任务神经网络
- 深度学习集成器
"""

from .lstm_predictor import LSTMPredictor
from .transformer_predictor import TransformerPredictor
from .multi_task_neural import MultiTaskNeuralPredictor
from .ensemble_deep_learning import EnsembleDeepLearning

__all__ = [
    "LSTMPredictor",
    "TransformerPredictor", 
    "MultiTaskNeuralPredictor",
    "EnsembleDeepLearning"
]
