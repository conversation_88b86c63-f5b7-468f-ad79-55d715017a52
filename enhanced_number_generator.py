#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的号码生成器 - 提升2+1命中率
"""

import numpy as np
import pandas as pd
from typing import List, Tuple
import random

class EnhancedNumberGenerator:
    """增强的号码生成器"""
    
    def __init__(self):
        self.strategies = {
            "hot_cold_balance": 0.3,
            "position_analysis": 0.25,
            "sum_control": 0.2,
            "span_control": 0.15,
            "ac_value_control": 0.1
        }
    
    def generate_red_balls(self, data: pd.DataFrame, kill_numbers: List[int], 
                          target_ratio: str) -> List[int]:
        """生成红球号码 - 多策略融合"""
        available_numbers = [i for i in range(1, 36) if i not in kill_numbers]
        
        if len(available_numbers) < 5:
            available_numbers = list(range(1, 36))
        
        # 策略1: 热冷平衡 (30%)
        hot_numbers = self.get_hot_numbers(data, available_numbers, count=15)
        cold_numbers = self.get_cold_numbers(data, available_numbers, count=10)
        
        # 策略2: 位置分析 (25%)
        position_preferred = self.get_position_preferred_numbers(data, available_numbers)
        
        # 策略3: 和值控制 (20%)
        sum_controlled = self.filter_by_sum_range(available_numbers, target_sum=(85, 115))
        
        # 策略4: 跨度控制 (15%)
        span_controlled = self.filter_by_span_range(available_numbers, target_span=(18, 28))
        
        # 综合选择
        candidate_pools = [hot_numbers, position_preferred, sum_controlled, span_controlled]
        weights = np.array([0.3, 0.25, 0.2, 0.15])
        weights = weights / weights.sum()  # 确保权重和为1

        selected_numbers = []
        for i in range(5):
            # 加权随机选择候选池
            pool_idx = np.random.choice(len(candidate_pools), p=weights)
            pool = candidate_pools[pool_idx]
            
            # 从选中的池中随机选择
            if pool and len([n for n in pool if n not in selected_numbers]) > 0:
                available_in_pool = [n for n in pool if n not in selected_numbers]
                selected_numbers.append(random.choice(available_in_pool))
            else:
                # 备选方案
                remaining = [n for n in available_numbers if n not in selected_numbers]
                if remaining:
                    selected_numbers.append(random.choice(remaining))
        
        # 确保满足目标比例
        selected_numbers = self.adjust_for_ratio(selected_numbers, target_ratio, available_numbers)
        
        return sorted(selected_numbers[:5])
    
    def get_hot_numbers(self, data: pd.DataFrame, available: List[int], count: int) -> List[int]:
        """获取热号"""
        recent_data = data.tail(20)
        number_counts = {}
        
        for _, row in recent_data.iterrows():
            for i in range(1, 6):
                num = row[f'红球{i}']
                if num in available:
                    number_counts[num] = number_counts.get(num, 0) + 1
        
        sorted_numbers = sorted(number_counts.items(), key=lambda x: x[1], reverse=True)
        return [num for num, _ in sorted_numbers[:count]]
    
    def get_cold_numbers(self, data: pd.DataFrame, available: List[int], count: int) -> List[int]:
        """获取冷号"""
        recent_data = data.tail(30)
        appeared_numbers = set()
        
        for _, row in recent_data.iterrows():
            for i in range(1, 6):
                appeared_numbers.add(row[f'红球{i}'])
        
        cold_numbers = [num for num in available if num not in appeared_numbers]
        return cold_numbers[:count] if cold_numbers else available[:count]
    
    def get_position_preferred_numbers(self, data: pd.DataFrame, available: List[int]) -> List[int]:
        """基于位置分析的首选号码"""
        position_weights = [1.0, 1.1, 1.2, 1.1, 1.0]  # 中间位置权重更高
        recent_data = data.tail(15)
        
        position_scores = {}
        for num in available:
            score = 0
            for _, row in recent_data.iterrows():
                for pos in range(5):
                    if row[f'红球{pos+1}'] == num:
                        score += position_weights[pos]
            position_scores[num] = score
        
        sorted_numbers = sorted(position_scores.items(), key=lambda x: x[1], reverse=True)
        return [num for num, _ in sorted_numbers[:15]]
    
    def filter_by_sum_range(self, numbers: List[int], target_sum: Tuple[int, int]) -> List[int]:
        """根据和值范围过滤号码"""
        # 简化实现：返回中等大小的号码
        return [num for num in numbers if 8 <= num <= 28]
    
    def filter_by_span_range(self, numbers: List[int], target_span: Tuple[int, int]) -> List[int]:
        """根据跨度范围过滤号码"""
        # 简化实现：避免极端号码
        return [num for num in numbers if 5 <= num <= 30]
    
    def adjust_for_ratio(self, numbers: List[int], target_ratio: str, available: List[int]) -> List[int]:
        """调整号码以满足目标比例"""
        if not target_ratio or ":" not in target_ratio:
            return numbers
        
        try:
            odd_target, even_target = map(int, target_ratio.split(":"))
            current_odd = sum(1 for n in numbers if n % 2 == 1)
            current_even = len(numbers) - current_odd
            
            # 如果比例不匹配，进行调整
            if current_odd != odd_target:
                # 简化调整逻辑
                adjusted = numbers.copy()
                if current_odd < odd_target:
                    # 需要更多奇数
                    for i, num in enumerate(adjusted):
                        if num % 2 == 0:  # 偶数
                            odd_candidates = [n for n in available if n % 2 == 1 and n not in adjusted]
                            if odd_candidates:
                                adjusted[i] = random.choice(odd_candidates)
                                break
                return adjusted
        except:
            pass
        
        return numbers
    
    def generate_blue_balls(self, data: pd.DataFrame, kill_numbers: List[int], 
                           target_ratio: str) -> List[int]:
        """生成蓝球号码"""
        available_numbers = [i for i in range(1, 13) if i not in kill_numbers]
        
        if len(available_numbers) < 2:
            available_numbers = list(range(1, 13))
        
        # 简单的热冷平衡策略
        recent_data = data.tail(15)
        blue_counts = {}
        
        for _, row in recent_data.iterrows():
            for i in range(1, 3):
                num = row[f'蓝球{i}']
                if num in available_numbers:
                    blue_counts[num] = blue_counts.get(num, 0) + 1
        
        # 选择适中频率的号码
        if blue_counts:
            sorted_blues = sorted(blue_counts.items(), key=lambda x: x[1])
            mid_range = sorted_blues[len(sorted_blues)//4:3*len(sorted_blues)//4]
            candidates = [num for num, _ in mid_range] if mid_range else available_numbers
        else:
            candidates = available_numbers
        
        # 随机选择2个
        selected = random.sample(candidates, min(2, len(candidates)))
        return sorted(selected)
