#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
增强特征测试脚本
测试新增的多维度特征对红球杀号准确性的提升效果
"""

import sys
import os

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

import pandas as pd
import numpy as np
from typing import List, Dict

# 导入系统模块
sys.path.append(os.path.join(project_root, 'src', 'apps'))
from advanced_probabilistic_system import (
    AdvancedProbabilisticSystem,
    AdvancedFeatureExtractor,
    SeasonalPatternAnalyzer,
    EnhancedDiversityKillSystem
)

def load_data():
    """加载彩票数据"""
    try:
        data_path = "data/raw/dlt_data.csv"
        data = pd.read_csv(data_path)
        print(f"✅ 成功加载数据文件 {data_path}: {len(data)} 期")
        return data
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return None

def test_feature_extraction():
    """测试特征提取功能"""
    print("\n🔧 测试特征提取功能...")
    
    data = load_data()
    if data is None:
        return
        
    # 初始化特征提取器
    feature_extractor = AdvancedFeatureExtractor(data)
    seasonal_analyzer = SeasonalPatternAnalyzer(data)
    
    # 测试期号
    test_period = "25068"
    
    print(f"\n📊 测试期号: {test_period}")
    
    # 1. 间隔特征测试
    print("\n1️⃣ 间隔特征测试:")
    interval_features = feature_extractor.extract_interval_features(test_period, 'red')
    print(f"   红球间隔特征 (前10个): {dict(list(interval_features.items())[:10])}")
    
    # 找出间隔最长的数字
    max_interval_balls = sorted(interval_features.items(), key=lambda x: x[1], reverse=True)[:5]
    print(f"   间隔最长的5个红球: {max_interval_balls}")
    
    # 2. 组合特征测试
    print("\n2️⃣ 组合特征测试:")
    combination_features = feature_extractor.extract_combination_features(test_period, 'red')
    print(f"   发现 {len(combination_features)} 个数字组合")
    
    # 显示最频繁的组合
    top_combinations = sorted(combination_features.items(), key=lambda x: x[1], reverse=True)[:5]
    print(f"   最频繁的5个组合: {top_combinations}")
    
    # 3. 统计特征测试
    print("\n3️⃣ 统计特征测试:")
    statistical_features = feature_extractor.extract_statistical_features(test_period)
    print(f"   统计特征: {statistical_features}")
    
    # 4. 季节性特征测试
    print("\n4️⃣ 季节性特征测试:")
    seasonal_weights = seasonal_analyzer.get_seasonal_weights(test_period, 'red')
    print(f"   季节性权重 (前10个): {dict(list(seasonal_weights.items())[:10])}")
    
    # 找出季节性权重最高的数字
    max_seasonal_balls = sorted(seasonal_weights.items(), key=lambda x: x[1], reverse=True)[:5]
    print(f"   季节性权重最高的5个红球: {max_seasonal_balls}")

def test_enhanced_kill_system():
    """测试增强杀号系统"""
    print("\n🎯 测试增强杀号系统...")
    
    data = load_data()
    if data is None:
        return
        
    # 转换为列表格式
    data_list = data.to_dict('records')
    
    # 初始化增强杀号系统
    enhanced_system = EnhancedDiversityKillSystem()
    
    # 测试几个期号
    test_periods = ["25068", "25067", "25066"]
    
    for period in test_periods:
        print(f"\n📅 测试期号: {period}")
        
        try:
            # 预测杀号
            red_kills = enhanced_system.predict_enhanced_kills(data_list, int(period), 13, 'red')
            blue_kills = enhanced_system.predict_enhanced_kills(data_list, int(period), 5, 'blue')
            
            print(f"   红球杀号: {red_kills}")
            print(f"   蓝球杀号: {blue_kills}")
            
            # 分析杀号分布
            if red_kills:
                small_count = sum(1 for x in red_kills if x <= 12)
                medium_count = sum(1 for x in red_kills if 13 <= x <= 24)
                large_count = sum(1 for x in red_kills if x >= 25)
                print(f"   红球分布 - 小区(1-12): {small_count}, 中区(13-24): {medium_count}, 大区(25-35): {large_count}")
                
                odd_count = sum(1 for x in red_kills if x % 2 == 1)
                even_count = len(red_kills) - odd_count
                print(f"   红球奇偶 - 奇数: {odd_count}, 偶数: {even_count}")
                
        except Exception as e:
            print(f"   ❌ 预测失败: {e}")

def compare_systems():
    """对比原系统和增强系统的效果"""
    print("\n⚖️ 对比原系统和增强系统...")
    
    data = load_data()
    if data is None:
        return
        
    # 转换为列表格式
    data_list = data.to_dict('records')
    
    # 初始化系统
    original_system = AdvancedProbabilisticSystem()
    enhanced_system = EnhancedDiversityKillSystem()
    
    test_periods = ["25068", "25067", "25066", "25065", "25064"]
    
    print(f"\n📊 对比测试 ({len(test_periods)} 期):")
    print("期号\t原系统红球杀号\t\t\t增强系统红球杀号")
    print("-" * 80)
    
    diversity_scores = {'original': [], 'enhanced': []}
    
    for period in test_periods:
        try:
            # 原系统预测
            original_kills = original_system.predict_kills_by_period(int(period))
            original_red = original_kills.get('red_kills', [])
            
            # 增强系统预测
            enhanced_red = enhanced_system.predict_enhanced_kills(data_list, int(period), 13, 'red')
            
            print(f"{period}\t{original_red[:8]}...\t{enhanced_red[:8]}...")
            
            # 计算多样性得分
            if original_red:
                orig_diversity = calculate_diversity_score(original_red)
                diversity_scores['original'].append(orig_diversity)
                
            if enhanced_red:
                enh_diversity = calculate_diversity_score(enhanced_red)
                diversity_scores['enhanced'].append(enh_diversity)
                
        except Exception as e:
            print(f"{period}\t❌ 预测失败: {e}")
            
    # 计算平均多样性
    if diversity_scores['original'] and diversity_scores['enhanced']:
        avg_orig = np.mean(diversity_scores['original'])
        avg_enh = np.mean(diversity_scores['enhanced'])
        
        print(f"\n📈 多样性对比:")
        print(f"   原系统平均多样性: {avg_orig:.3f}")
        print(f"   增强系统平均多样性: {avg_enh:.3f}")
        print(f"   提升幅度: {((avg_enh - avg_orig) / avg_orig * 100):.1f}%")

def calculate_diversity_score(numbers: List[int]) -> float:
    """计算数字序列的多样性得分"""
    if not numbers:
        return 0.0
        
    # 区间分布多样性
    small_count = sum(1 for x in numbers if x <= 12)
    medium_count = sum(1 for x in numbers if 13 <= x <= 24)
    large_count = sum(1 for x in numbers if x >= 25)
    
    total = len(numbers)
    region_entropy = 0
    for count in [small_count, medium_count, large_count]:
        if count > 0:
            p = count / total
            region_entropy -= p * np.log2(p)
            
    # 奇偶分布多样性
    odd_count = sum(1 for x in numbers if x % 2 == 1)
    even_count = total - odd_count
    
    parity_entropy = 0
    for count in [odd_count, even_count]:
        if count > 0:
            p = count / total
            parity_entropy -= p * np.log2(p)
            
    # 综合多样性得分 (0-1)
    max_region_entropy = np.log2(3)  # 3个区间的最大熵
    max_parity_entropy = np.log2(2)  # 2个奇偶类别的最大熵
    
    diversity_score = (region_entropy / max_region_entropy + parity_entropy / max_parity_entropy) / 2
    
    return diversity_score

def main():
    """主函数"""
    print("🚀 启动增强特征测试...")
    print("=" * 60)
    
    # 1. 测试特征提取
    test_feature_extraction()
    
    # 2. 测试增强杀号系统
    test_enhanced_kill_system()
    
    # 3. 对比系统效果
    compare_systems()
    
    print("\n✅ 增强特征测试完成!")

if __name__ == "__main__":
    main()
