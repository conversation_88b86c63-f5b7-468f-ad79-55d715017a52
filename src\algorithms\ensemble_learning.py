"""
集成学习框架 - Phase 4 实现
Ensemble Learning Framework - Phase 4 Implementation

结合多种算法的集成学习系统:
1. 增强杀号算法 (94% 多样性)
2. 增强马尔科夫-贝叶斯预测器 (45% 准确率)
3. 传统算法作为备选
4. 动态权重调整
5. 投票机制
"""

import numpy as np
from typing import Dict, List, Tuple, Any, Optional
from collections import defaultdict
import logging
from dataclasses import dataclass
import pandas as pd

@dataclass
class AlgorithmPerformance:
    """算法性能跟踪"""
    name: str
    accuracy: float = 0.0
    hit_count: int = 0
    total_count: int = 0
    weight: float = 1.0
    confidence: float = 0.5
    recent_performance: List[bool] = None
    
    def __post_init__(self):
        if self.recent_performance is None:
            self.recent_performance = []
    
    def update_performance(self, hit: bool):
        """更新性能指标"""
        self.recent_performance.append(hit)
        if len(self.recent_performance) > 10:  # 保持最近10次记录
            self.recent_performance.pop(0)
        
        self.hit_count += 1 if hit else 0
        self.total_count += 1
        self.accuracy = self.hit_count / self.total_count if self.total_count > 0 else 0.0
        
        # 基于最近表现调整置信度
        if len(self.recent_performance) >= 5:
            recent_accuracy = sum(self.recent_performance[-5:]) / 5
            self.confidence = min(0.9, max(0.1, recent_accuracy))


class EnsembleLearningManager:
    """集成学习管理器"""
    
    def __init__(self):
        """初始化集成学习管理器"""
        self.algorithms = {}
        self.performance_tracker = {}
        self.voting_weights = {}
        self.ensemble_history = []
        
        # 性能监控配置
        self.min_samples_for_weight_update = 5
        self.weight_decay_factor = 0.95
        self.performance_window = 10
        
        # 日志配置
        logging.basicConfig(level=logging.INFO)  # 恢复正常日志级别
        self.logger = logging.getLogger(__name__)
    
    def register_algorithm(self, name: str, algorithm: Any, initial_weight: float = 1.0):
        """注册算法到集成系统"""
        self.algorithms[name] = algorithm
        self.performance_tracker[name] = AlgorithmPerformance(
            name=name, 
            weight=initial_weight
        )
        self.voting_weights[name] = initial_weight
        
        self.logger.info(f"✅ 算法 '{name}' 已注册到集成系统，初始权重: {initial_weight}")
    
    def update_algorithm_performance(self, name: str, hit: bool, confidence: float = None):
        """更新算法性能"""
        if name not in self.performance_tracker:
            self.logger.warning(f"⚠️ 算法 '{name}' 未注册")
            return
        
        perf = self.performance_tracker[name]
        perf.update_performance(hit)
        
        if confidence is not None:
            perf.confidence = confidence
        
        # 动态调整权重
        self._update_dynamic_weights()
        
        self.logger.info(f"📊 算法 '{name}' 性能更新: 准确率={perf.accuracy:.2%}, 权重={perf.weight:.3f}")
    
    def _update_dynamic_weights(self):
        """动态更新算法权重"""
        total_performance = 0
        valid_algorithms = []
        
        for name, perf in self.performance_tracker.items():
            if perf.total_count >= self.min_samples_for_weight_update:
                # 综合考虑准确率和置信度
                combined_score = perf.accuracy * perf.confidence
                total_performance += combined_score
                valid_algorithms.append((name, combined_score))
        
        if total_performance > 0:
            # 重新分配权重
            for name, score in valid_algorithms:
                new_weight = score / total_performance
                # 应用权重衰减，避免过度调整
                old_weight = self.voting_weights[name]
                self.voting_weights[name] = (
                    old_weight * self.weight_decay_factor + 
                    new_weight * (1 - self.weight_decay_factor)
                )
                self.performance_tracker[name].weight = self.voting_weights[name]
    
    def ensemble_predict(self, prediction_data: Dict[str, Any]) -> Dict[str, Any]:
        """集成预测"""
        predictions = {}
        confidences = {}

        # 准备算法输入数据
        data = prediction_data.get('data')
        if data is None or len(data) == 0:
            return {'error': '没有可用的训练数据'}

        # 收集各算法预测结果
        for name, algorithm in self.algorithms.items():
            try:
                result = self._call_algorithm(name, algorithm, prediction_data)
                if result is not None:
                    predictions[name] = result

                    # 获取置信度
                    if isinstance(result, dict) and 'confidence' in result:
                        confidences[name] = result['confidence']
                    elif isinstance(result, tuple) and len(result) == 2:
                        # (prediction, confidence) 格式
                        confidences[name] = result[1]
                    else:
                        confidences[name] = self.performance_tracker[name].confidence
                else:
                    # 算法返回None，记录为失败
                    self.logger.warning(f"⚠️ 算法 '{name}' 返回空结果")
                    # 立即更新性能，记录为失败
                    self.update_algorithm_performance(name, False, 0.1)

            except Exception as e:
                self.logger.error(f"❌ 算法 '{name}' 预测失败: {e}")
                # 立即更新性能，记录为失败
                self.update_algorithm_performance(name, False, 0.1)
                continue
        
        # 执行加权投票
        ensemble_result = self._weighted_voting(predictions, confidences)
        
        # 记录集成历史
        self.ensemble_history.append({
            'predictions': predictions,
            'confidences': confidences,
            'weights': self.voting_weights.copy(),
            'result': ensemble_result
        })
        
        return ensemble_result

    def predict(self, data: pd.DataFrame, prediction_type: str = 'red_odd_even_ratio') -> Dict[str, Any]:
        """
        简化的预测接口

        Args:
            data: 历史数据
            prediction_type: 预测类型

        Returns:
            预测结果
        """
        prediction_data = {
            'data': data,
            'prediction_type': prediction_type
        }
        return self.ensemble_predict(prediction_data)

    def _call_algorithm(self, name: str, algorithm: Any, prediction_data: Dict[str, Any]) -> Any:
        """
        统一算法调用接口

        Args:
            name: 算法名称
            algorithm: 算法实例
            prediction_data: 预测数据

        Returns:
            算法预测结果
        """
        data = prediction_data.get('data')

        # 增强马尔科夫-贝叶斯预测器
        if hasattr(algorithm, 'predict_next_state') and 'enhanced' in name:
            # 准备最近的状态序列
            recent_states = self._prepare_states_for_enhanced_algorithm(data, name)
            if recent_states:
                return algorithm.predict_next_state(recent_states)
            else:
                return None

        # 传统马尔科夫模型
        elif hasattr(algorithm, 'predict_next_state') and 'markov' in name:
            # 准备状态序列
            recent_states = self._prepare_states_for_markov(data, name)
            if recent_states:
                return algorithm.predict_next_state(recent_states)
            else:
                return None

        # 贝叶斯选择器
        elif hasattr(algorithm, 'select') and 'bayes' in name:
            # 贝叶斯选择器通常用于选择号码，这里返回一个模拟结果
            return self._get_bayes_prediction(name)

        # 传统贝叶斯算法（BayesSelector）
        elif hasattr(algorithm, 'select_best_state') and 'bayes' in name:
            try:
                # 准备似然概率源（使用简单的均匀分布作为似然）
                likelihood_sources = [{}]

                # 根据特征类型获取所有可能状态
                if algorithm.feature_type == 'red':
                    from src.utils.utils import get_all_red_states
                    all_states = get_all_red_states()
                else:
                    from src.utils.utils import get_all_blue_states
                    all_states = get_all_blue_states()

                # 创建均匀似然分布
                uniform_prob = 1.0 / len(all_states)
                likelihood_sources[0] = {state: uniform_prob for state in all_states}

                # 调用贝叶斯选择器
                best_state, confidence = algorithm.select_best_state(likelihood_sources)

                return {'prediction': best_state, 'confidence': confidence}

            except Exception as e:
                self.logger.error(f"❌ 传统贝叶斯算法调用失败: {e}")
                return None

        # 数据驱动预测器
        elif hasattr(algorithm, 'predict_ratio') and 'data_driven' in name:
            try:
                # 获取预测类型
                prediction_type = prediction_data.get('prediction_type', 'red_odd_even_ratio')
                if 'odd_even' in prediction_type:
                    ratio_type = 'odd_even'
                elif 'size' in prediction_type:
                    ratio_type = 'size'
                else:
                    ratio_type = 'odd_even'  # 默认

                result = algorithm.predict_ratio(data, ratio_type)
                return result
            except Exception as e:
                self.logger.error(f"❌ 数据驱动预测器调用失败: {e}")
                return None

        # 多样化红球预测器
        elif hasattr(algorithm, 'predict_ratio') and 'diversified' in name:
            try:
                # 获取预测类型
                prediction_type = prediction_data.get('prediction_type', 'red_odd_even_ratio')
                if 'odd_even' in prediction_type:
                    ratio_type = 'odd_even'
                elif 'size' in prediction_type:
                    ratio_type = 'size'
                else:
                    ratio_type = 'odd_even'  # 默认

                result = algorithm.predict_ratio(data, ratio_type)
                return result
            except Exception as e:
                self.logger.error(f"❌ 多样化预测器调用失败: {e}")
                return None

        # 红球奇偶比专用优化器
        elif hasattr(algorithm, 'predict_ratio') and 'specialized' in name:
            try:
                # 获取预测类型
                prediction_type = prediction_data.get('prediction_type', 'red_odd_even_ratio')
                if 'odd_even' in prediction_type:
                    ratio_type = 'odd_even'
                elif 'size' in prediction_type:
                    ratio_type = 'size'
                else:
                    ratio_type = 'odd_even'  # 默认

                result = algorithm.predict_ratio(data, ratio_type)
                return result
            except Exception as e:
                self.logger.error(f"❌ 专用优化器调用失败: {e}")
                return None

        # 增强杀号算法
        elif hasattr(algorithm, 'generate_kill_numbers') and 'kill' in name:
            try:
                # 杀号算法需要期号参数，这里使用最新期号
                latest_period = data['期号'].iloc[-1] if len(data) > 0 else 25068
                # 确保期号是整数类型
                latest_period = int(latest_period)

                # 检查方法签名，适配不同的参数需求
                if hasattr(algorithm, 'generate_red_kill_numbers'):
                    # 新的统一杀号算法
                    red_kills = algorithm.generate_red_kill_numbers(data, latest_period, 5)
                    blue_kills = algorithm.generate_blue_kill_numbers(data, latest_period, 2)
                    return {'kill_numbers': red_kills + blue_kills, 'confidence': 0.8}
                else:
                    # 旧的杀号算法
                    kill_numbers = algorithm.generate_kill_numbers(data, latest_period, 5, 2)
                    return {'kill_numbers': kill_numbers, 'confidence': 0.8}
            except Exception as e:
                self.logger.error(f"❌ 杀号算法调用失败: {e}")
                return None

        # 多样化红球预测器
        elif hasattr(algorithm, 'predict_ratio') and 'diversified' in name:
            try:
                # 提取特征类型
                if 'red_odd_even' in name:
                    ratio_type = 'odd_even'
                elif 'red_size' in name:
                    ratio_type = 'size'
                else:
                    ratio_type = 'odd_even'  # 默认

                # 调用多样化预测器
                result = algorithm.predict_ratio(data, ratio_type)
                return result
            except Exception as e:
                self.logger.error(f"❌ 多样化预测器调用失败: {e}")
                return None

        # 红球奇偶比专用优化器
        elif hasattr(algorithm, 'predict_ratio') and 'red_odd_even_optimizer' in name:
            try:
                result = algorithm.predict_ratio(data, 'odd_even')
                return result
            except Exception as e:
                self.logger.error(f"❌ 红球奇偶比优化器调用失败: {e}")
                return None

        # 通用predict方法
        elif hasattr(algorithm, 'predict'):
            return algorithm.predict(prediction_data)

        else:
            self.logger.warning(f"⚠️ 算法 '{name}' 没有可识别的预测方法")
            return None

    def _extract_feature_name(self, algorithm_name: str) -> str:
        """从算法名称中提取特征名称"""
        if 'red_odd_even' in algorithm_name:
            return 'red_odd_even'
        elif 'red_size' in algorithm_name:
            return 'red_size'
        elif 'blue_size' in algorithm_name:
            return 'blue_size'
        else:
            return 'red_odd_even'  # 默认特征

    def _get_current_state_for_feature(self, data: pd.DataFrame, feature_name: str) -> str:
        """获取特定特征的当前状态"""
        try:
            from src.utils.utils import parse_numbers, calculate_odd_even_ratio, calculate_size_ratio_red, calculate_size_ratio_blue, ratio_to_state

            if len(data) == 0:
                return "3:2"  # 默认状态

            # 获取最新一期数据
            latest_row = data.iloc[-1]
            red_balls, blue_balls = parse_numbers(latest_row)

            if 'red_odd_even' in feature_name:
                ratio = calculate_odd_even_ratio(red_balls)
                return ratio_to_state(ratio)
            elif 'red_size' in feature_name:
                ratio = calculate_size_ratio_red(red_balls)
                return ratio_to_state(ratio)
            elif 'blue_size' in feature_name:
                ratio = calculate_size_ratio_blue(blue_balls)
                return ratio_to_state(ratio)
            else:
                return "3:2"  # 默认状态

        except Exception as e:
            self.logger.error(f"获取当前状态失败: {e}")
            return "3:2"  # 默认状态

    def _prepare_states_for_enhanced_algorithm(self, data: pd.DataFrame, name: str) -> List[str]:
        """为增强算法准备状态序列"""
        try:
            from src.utils.utils import parse_numbers, calculate_odd_even_ratio, calculate_size_ratio_red, calculate_size_ratio_blue, ratio_to_state

            states = []
            # 取最近100期数据
            recent_data = data.tail(100)

            for _, row in recent_data.iterrows():
                red_balls, blue_balls = parse_numbers(row)

                if 'red_odd_even' in name:
                    ratio = calculate_odd_even_ratio(red_balls)
                    state = ratio_to_state(ratio)
                elif 'red_size' in name:
                    ratio = calculate_size_ratio_red(red_balls)
                    state = ratio_to_state(ratio)
                elif 'blue_size' in name:
                    ratio = calculate_size_ratio_blue(blue_balls)
                    state = ratio_to_state(ratio)
                else:
                    continue

                states.append(state)

            return states[-10:] if len(states) >= 10 else states  # 返回最近10个状态

        except Exception as e:
            self.logger.error(f"❌ 为增强算法 '{name}' 准备状态失败: {e}")
            return []

    def _prepare_states_for_markov(self, data: pd.DataFrame, name: str) -> List[str]:
        """为传统马尔科夫模型准备状态序列"""
        try:
            from src.utils.utils import parse_numbers, calculate_odd_even_ratio, calculate_size_ratio_red, calculate_size_ratio_blue, ratio_to_state

            states = []
            # 取所有数据进行训练
            for _, row in data.iterrows():
                red_balls, blue_balls = parse_numbers(row)

                if 'red_odd_even' in name:
                    ratio = calculate_odd_even_ratio(red_balls)
                    state = ratio_to_state(ratio)
                elif 'red_size' in name:
                    ratio = calculate_size_ratio_red(red_balls)
                    state = ratio_to_state(ratio)
                elif 'blue_size' in name:
                    ratio = calculate_size_ratio_blue(blue_balls)
                    state = ratio_to_state(ratio)
                else:
                    continue

                states.append(state)

            return states[-5:] if len(states) >= 5 else states  # 返回最近5个状态

        except Exception as e:
            self.logger.error(f"❌ 为马尔科夫模型 '{name}' 准备状态失败: {e}")
            return []

    def _get_bayes_prediction(self, name: str) -> Dict[str, Any]:
        """为贝叶斯选择器生成预测结果（已弃用，应使用实际算法调用）"""
        # 这个方法已经不应该被调用，因为我们现在直接调用算法
        self.logger.warning(f"⚠️ _get_bayes_prediction被调用，这不应该发生: {name}")
        return {'prediction': 'unknown', 'confidence': 0.1}

    def _weighted_voting(self, predictions: Dict[str, Any], confidences: Dict[str, float]) -> Dict[str, Any]:
        """加权投票机制"""
        if not predictions:
            return {'error': '没有有效的预测结果'}

        # 分类收集预测结果
        red_odd_even_votes = {}
        red_size_votes = {}
        blue_size_votes = {}
        kill_numbers = []

        # 处理各算法的预测结果
        for name, result in predictions.items():
            weight = self.voting_weights.get(name, 1.0)
            confidence = confidences.get(name, 0.5)
            combined_weight = weight * confidence

            # 处理不同格式的结果
            prediction_value = self._extract_prediction_value(result, name)

            # 调试信息：显示每个算法的预测结果
            self.logger.debug(f"🔍 算法 '{name}' 预测值: {prediction_value}, 权重: {combined_weight:.3f}")

            if prediction_value:
                if 'red_odd_even' in name:
                    red_odd_even_votes[prediction_value] = red_odd_even_votes.get(prediction_value, 0) + combined_weight
                elif 'red_size' in name:
                    red_size_votes[prediction_value] = red_size_votes.get(prediction_value, 0) + combined_weight
                elif 'blue_size' in name:
                    blue_size_votes[prediction_value] = blue_size_votes.get(prediction_value, 0) + combined_weight
                elif 'kill' in name and isinstance(result, dict) and 'kill_numbers' in result:
                    kill_numbers.extend(result['kill_numbers'])

        # 选择预测结果（红球奇偶比使用概率采样增加多样性）
        result = {}

        if red_odd_even_votes:
            # 对红球奇偶比使用概率采样增加多样性
            result['red_odd_even_ratio'] = self._probabilistic_vote_selection(red_odd_even_votes)
            result['red_odd_even_ratio_confidence'] = max(red_odd_even_votes.values()) / sum(red_odd_even_votes.values())

        if red_size_votes:
            result['red_size_ratio'] = max(red_size_votes, key=red_size_votes.get)
            result['red_size_ratio_confidence'] = max(red_size_votes.values()) / sum(red_size_votes.values())

        if blue_size_votes:
            result['blue_size_ratio'] = max(blue_size_votes, key=blue_size_votes.get)
            result['blue_size_ratio_confidence'] = max(blue_size_votes.values()) / sum(blue_size_votes.values())

        if kill_numbers:
            # 去重并限制杀号数量
            unique_kills = list(set(kill_numbers))[:13]  # 最多13个红球杀号
            result['kill_numbers'] = unique_kills

        # 计算总体置信度
        total_confidence = sum(confidences.values()) / len(confidences) if confidences else 0.0

        # 添加集成信息
        result['ensemble_info'] = {
            'participating_algorithms': list(predictions.keys()),
            'algorithm_weights': {name: self.voting_weights.get(name, 1.0) for name in predictions.keys()},
            'total_confidence': total_confidence
        }

        return result

    def _probabilistic_vote_selection(self, votes: Dict[str, float]) -> str:
        """
        智能概率采样选择预测结果（平衡多样性和准确性）

        Args:
            votes: 投票结果 {预测值: 权重}

        Returns:
            str: 选择的预测结果
        """
        import random
        import numpy as np

        if not votes:
            return "3:2"  # 默认值

        # 计算总权重
        total_weight = sum(votes.values())
        if total_weight <= 0:
            return max(votes.keys())  # 返回任意一个

        # 归一化为概率
        probabilities = {pred: weight / total_weight for pred, weight in votes.items()}

        # 智能平滑处理：根据算法质量调整平滑强度
        max_prob = max(probabilities.values())

        if max_prob > 0.85:
            # 强平滑：算法过度一致时（提高阈值）
            smoothing_factor = 0.3  # 降低平滑强度
            uniform_prob = 1.0 / len(probabilities)
            for pred in probabilities:
                probabilities[pred] = (1 - smoothing_factor) * probabilities[pred] + smoothing_factor * uniform_prob
        elif max_prob > 0.7:
            # 轻度平滑：适度集中时
            smoothing_factor = 0.15  # 更轻的平滑
            uniform_prob = 1.0 / len(probabilities)
            for pred in probabilities:
                probabilities[pred] = (1 - smoothing_factor) * probabilities[pred] + smoothing_factor * uniform_prob

        # 智能多样性增强：基于算法质量调整随机性
        diversity_boost_chance = 0.1  # 大幅降低随机性，从30%降到10%

        # 如果有明显的高置信度预测，进一步减少随机干扰
        high_confidence_predictions = [pred for pred, prob in probabilities.items() if prob > 0.5]
        if high_confidence_predictions:
            diversity_boost_chance = 0.05  # 进一步降低随机性

        # 计算算法一致性：如果算法们比较一致，减少随机干扰
        prob_variance = np.var(list(probabilities.values()))
        if prob_variance < 0.01:  # 算法预测非常一致
            diversity_boost_chance = 0.02  # 几乎不进行随机干扰

        if len(probabilities) > 1 and random.random() < diversity_boost_chance:
            sorted_probs = sorted(probabilities.items(), key=lambda x: x[1])
            if len(sorted_probs) > 2:
                # 提升第二低概率的选项，而不是最低的（更合理）
                second_lowest_pred = sorted_probs[1][0]
                probabilities[second_lowest_pred] *= 1.2  # 降低提升倍数
            else:
                # 只有两个选项时，提升较低的
                lowest_pred = sorted_probs[0][0]
                probabilities[lowest_pred] *= 1.1

            # 重新归一化
            total_prob = sum(probabilities.values())
            for pred in probabilities:
                probabilities[pred] /= total_prob

        # 基于调整后的概率进行采样
        predictions = list(probabilities.keys())
        probs = list(probabilities.values())

        # 使用numpy进行概率采样
        try:
            selected = np.random.choice(predictions, p=probs)
            return selected
        except:
            # 如果采样失败，返回最高权重的选择
            return max(votes.items(), key=lambda x: x[1])[0]

    def _extract_prediction_value(self, result: Any, name: str) -> str:
        """从预测结果中提取预测值"""
        if isinstance(result, dict):
            if 'prediction' in result:
                return result['prediction']
            elif 'red_odd_even_ratio' in result:
                return result['red_odd_even_ratio']
            elif 'red_size_ratio' in result:
                return result['red_size_ratio']
            elif 'blue_size_ratio' in result:
                return result['blue_size_ratio']
        elif isinstance(result, tuple) and len(result) >= 1:
            return str(result[0])  # 取第一个元素作为预测值
        elif isinstance(result, str):
            return result

        return None

    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        summary = {
            'algorithms': {},
            'ensemble_stats': {
                'total_predictions': len(self.ensemble_history),
                'average_confidence': 0.0
            }
        }

        for name, perf in self.performance_tracker.items():
            summary['algorithms'][name] = {
                'accuracy': perf.accuracy,
                'total_predictions': perf.total_count,
                'current_weight': perf.weight,
                'confidence': perf.confidence,
                'recent_performance': perf.recent_performance[-5:] if perf.recent_performance else []
            }

        if self.ensemble_history:
            confidences = [h.get('result', {}).get('ensemble_info', {}).get('total_confidence', 0.5)
                          for h in self.ensemble_history]
            summary['ensemble_stats']['average_confidence'] = np.mean(confidences)

        return summary

    def reset_performance(self):
        """重置性能统计"""
        for perf in self.performance_tracker.values():
            perf.hit_count = 0
            perf.total_count = 0
            perf.accuracy = 0.0
            perf.recent_performance = []

        self.ensemble_history = []
        self.logger.info("🔄 集成学习性能统计已重置")
