#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合命中率优化器
基于回测分析结果，针对性改进预测系统的命中率

主要问题分析：
1. 比例预测准确率低 (35.3%)
2. 红球奇偶比预测过于固化 (总是3:2)
3. 2+1命中率为0
4. 集成融合缺乏多样性

改进策略：
1. 比例预测多样化
2. 集成权重优化
3. 号码生成策略改进
4. 新增预测维度
"""

import os
import sys
import json
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Tuple, Any
import logging

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class ComprehensiveHitRateOptimizer:
    """综合命中率优化器"""
    
    def __init__(self):
        self.predictor = None
        self.optimization_results = {}
        
        # 设置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
    def load_backtest_results(self) -> Dict:
        """加载最新的回测结果"""
        results_dir = "optimization_results"
        if not os.path.exists(results_dir):
            raise FileNotFoundError("未找到回测结果目录")
            
        # 查找最新的回测结果文件
        backtest_files = [f for f in os.listdir(results_dir) if f.startswith("backtest_results_")]
        if not backtest_files:
            raise FileNotFoundError("未找到回测结果文件")
            
        latest_file = max(backtest_files)
        file_path = os.path.join(results_dir, latest_file)
        
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def analyze_prediction_patterns(self, backtest_results: Dict) -> Dict:
        """分析预测模式，识别问题"""
        analysis = {
            "ratio_predictions": {
                "red_odd_even": [],
                "red_size": [],
                "blue_size": []
            },
            "confidence_levels": [],
            "prediction_diversity": {},
            "common_patterns": {}
        }
        
        # 分析每期的预测模式
        for period_data in backtest_results.get("detailed_results", []):
            prediction = period_data.get("prediction", {})
            
            # 收集比例预测
            if "红球奇偶" in prediction:
                analysis["ratio_predictions"]["red_odd_even"].append(prediction["红球奇偶"])
            if "红球大小" in prediction:
                analysis["ratio_predictions"]["red_size"].append(prediction["红球大小"])
            if "蓝球大小" in prediction:
                analysis["ratio_predictions"]["blue_size"].append(prediction["蓝球大小"])
        
        # 计算预测多样性
        for ratio_type, predictions in analysis["ratio_predictions"].items():
            unique_predictions = set(predictions)
            analysis["prediction_diversity"][ratio_type] = {
                "unique_count": len(unique_predictions),
                "total_count": len(predictions),
                "diversity_ratio": len(unique_predictions) / len(predictions) if predictions else 0,
                "most_common": max(unique_predictions, key=predictions.count) if predictions else None,
                "most_common_frequency": predictions.count(max(unique_predictions, key=predictions.count)) / len(predictions) if predictions else 0
            }
        
        return analysis
    
    def create_enhanced_ratio_predictor(self) -> Dict:
        """创建增强的比例预测器配置"""
        enhanced_config = {
            "diversified_ratio_predictor": {
                "weight": 2.0,
                "strategies": [
                    {
                        "name": "historical_frequency",
                        "weight": 0.3,
                        "lookback_periods": [10, 20, 50]
                    },
                    {
                        "name": "trend_analysis",
                        "weight": 0.25,
                        "trend_periods": [5, 10, 15]
                    },
                    {
                        "name": "cycle_detection",
                        "weight": 0.2,
                        "cycle_lengths": [7, 14, 21]
                    },
                    {
                        "name": "anti_pattern",
                        "weight": 0.15,
                        "anti_streak_threshold": 3
                    },
                    {
                        "name": "random_diversity",
                        "weight": 0.1,
                        "diversity_factor": 0.2
                    }
                ]
            }
        }
        return enhanced_config
    
    def create_enhanced_number_generator(self) -> Dict:
        """创建增强的号码生成器配置"""
        enhanced_config = {
            "multi_strategy_generator": {
                "strategies": [
                    {
                        "name": "hot_cold_balance",
                        "weight": 0.3,
                        "hot_ratio": 0.6,
                        "cold_ratio": 0.4
                    },
                    {
                        "name": "position_analysis",
                        "weight": 0.25,
                        "position_weights": [1.0, 1.1, 1.2, 1.1, 1.0]  # 红球位置权重
                    },
                    {
                        "name": "sum_control",
                        "weight": 0.2,
                        "target_sum_range": (85, 115),
                        "tolerance": 10
                    },
                    {
                        "name": "span_control",
                        "weight": 0.15,
                        "target_span_range": (18, 28),
                        "tolerance": 5
                    },
                    {
                        "name": "ac_value_control",
                        "weight": 0.1,
                        "target_ac_range": (3, 8)
                    }
                ]
            }
        }
        return enhanced_config
    
    def optimize_ensemble_weights(self, backtest_results: Dict) -> Dict:
        """基于回测结果优化集成权重"""
        # 分析各算法的表现
        algorithm_performance = {}
        
        # 根据回测建议调整权重
        optimized_weights = {
            # 降低ML预测器权重（从2.5到2.0）
            "ml_ratio_predictor": 2.0,
            
            # 增强表现较好的算法权重
            "enhanced_kill": 2.2,  # 杀号表现很好，增加权重
            "enhanced_red_odd_even": 1.8,  # 适度增加
            "enhanced_red_size": 1.8,
            "enhanced_blue_size": 1.8,
            
            # 传统算法保持稳定
            "traditional_markov_red_odd_even": 1.0,
            "traditional_markov_red_size": 1.0,
            "traditional_markov_blue_size": 1.0,
            
            # 专家系统权重调整
            "red_odd_even_specialist": 2.0,  # 降低过度自信
            
            # 数据驱动算法权重优化
            "enhanced_historical_frequency": 2.0,
            "enhanced_transition_pattern": 1.8,
            "enhanced_correlation": 1.6,
            "enhanced_multi_time_window": 1.9
        }
        
        return optimized_weights
    
    def implement_diversity_enhancement(self) -> Dict:
        """实施多样性增强策略"""
        diversity_config = {
            "confidence_calibration": {
                "max_confidence": 0.8,  # 降低过度自信
                "min_confidence": 0.3,
                "calibration_factor": 0.7
            },
            "prediction_randomization": {
                "random_factor": 0.15,  # 增加15%的随机性
                "anti_streak_threshold": 3,  # 避免连续相同预测
                "diversity_bonus": 0.1
            },
            "adaptive_threshold": {
                "performance_window": 10,
                "adjustment_rate": 0.05,
                "min_threshold": 0.4,
                "max_threshold": 0.9
            }
        }
        return diversity_config
    
    def create_new_prediction_dimensions(self) -> Dict:
        """创建新的预测维度"""
        new_dimensions = {
            "consecutive_analysis": {
                "max_consecutive": 3,
                "consecutive_penalty": 0.8
            },
            "ac_value_prediction": {
                "target_range": (3, 8),
                "weight": 0.15
            },
            "sum_prediction": {
                "red_sum_range": (85, 115),
                "blue_sum_range": (10, 16),
                "weight": 0.2
            },
            "span_prediction": {
                "red_span_range": (18, 28),
                "blue_span_range": (5, 11),
                "weight": 0.15
            },
            "pattern_breaking": {
                "break_probability": 0.3,
                "pattern_memory": 5
            }
        }
        return new_dimensions
    
    def run_optimization(self) -> Dict:
        """运行综合优化"""
        print("🚀 开始综合命中率优化")
        print("=" * 60)
        
        # 1. 加载回测结果
        print("📊 第一步: 加载回测分析结果")
        try:
            backtest_results = self.load_backtest_results()
            print(f"✅ 成功加载回测结果")
        except Exception as e:
            print(f"❌ 加载回测结果失败: {e}")
            return {}
        
        # 2. 分析预测模式
        print("\n🔍 第二步: 分析预测模式问题")
        pattern_analysis = self.analyze_prediction_patterns(backtest_results)
        
        # 显示关键问题
        print("📈 发现的主要问题:")
        for ratio_type, diversity_info in pattern_analysis["prediction_diversity"].items():
            print(f"  {ratio_type}: 多样性 {diversity_info['diversity_ratio']:.1%}, "
                  f"最常见预测 {diversity_info['most_common']} "
                  f"({diversity_info['most_common_frequency']:.1%})")
        
        # 3. 生成优化配置
        print("\n⚙️ 第三步: 生成优化配置")
        optimization_config = {
            "enhanced_ratio_predictor": self.create_enhanced_ratio_predictor(),
            "enhanced_number_generator": self.create_enhanced_number_generator(),
            "optimized_weights": self.optimize_ensemble_weights(backtest_results),
            "diversity_enhancement": self.implement_diversity_enhancement(),
            "new_dimensions": self.create_new_prediction_dimensions(),
            "optimization_timestamp": datetime.now().isoformat(),
            "target_improvements": {
                "ratio_accuracy": "35.3% → 50%+",
                "hit_rate_2_plus_1": "0% → 10%+",
                "overall_score": "0.512 (D) → 0.65+ (C)",
                "diversity_ratio": "提升预测多样性至80%+"
            }
        }
        
        # 4. 保存优化配置
        print("\n💾 第四步: 保存优化配置")
        os.makedirs("optimization_results", exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        config_file = f"optimization_results/hit_rate_optimization_config_{timestamp}.json"
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(optimization_config, f, ensure_ascii=False, indent=2)
        
        print(f"📁 优化配置已保存: {config_file}")
        
        # 5. 生成实施建议
        print("\n📋 第五步: 生成实施建议")
        implementation_plan = self.generate_implementation_plan(optimization_config)
        
        print("\n🎯 优化完成! 主要改进方向:")
        print("1. 🔧 比例预测多样化 - 解决固化预测问题")
        print("2. ⚖️ 集成权重优化 - 基于实际表现调整")
        print("3. 🎲 号码生成增强 - 提升2+1命中率")
        print("4. 📊 新增预测维度 - 增加分析深度")
        print("5. 🎨 多样性增强 - 避免过度自信")
        
        return optimization_config
    
    def generate_implementation_plan(self, config: Dict) -> List[str]:
        """生成实施计划"""
        plan = [
            "立即实施 (高优先级):",
            "- 修改比例预测算法，增加多样性策略",
            "- 调整ML预测器权重从2.5到2.0",
            "- 实施置信度校准，避免过度自信",
            "",
            "短期实施 (中优先级):",
            "- 增强号码生成策略，加入热冷平衡",
            "- 实施新的预测维度 (AC值、和值、跨度)",
            "- 优化集成融合算法",
            "",
            "长期优化 (持续改进):",
            "- 建立动态权重调整机制",
            "- 增加更多历史模式分析",
            "- 实施自适应学习算法"
        ]
        
        for item in plan:
            print(f"  {item}")
        
        return plan

if __name__ == "__main__":
    optimizer = ComprehensiveHitRateOptimizer()
    results = optimizer.run_optimization()
